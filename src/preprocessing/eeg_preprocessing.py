"""
EEG Data Preprocessing Pipeline
Implements filtering, epoching, and feature extraction for motor imagery data
"""

import numpy as np # type: ignore
import mne # type: ignore
from mne import Epochs # type: ignore
from mne.time_frequency import psd_array_welch # type: ignore
import matplotlib.pyplot as plt # type: ignore
from sklearn.base import BaseEstimator, TransformerMixin # type: ignore


class EEGPreprocessor(BaseEstimator, TransformerMixin):
    """
    EEG preprocessing pipeline for motor imagery data
    
    This class handles:
    - Loading raw EEG data
    - Filtering (bandpass, notch)
    - Epoching around events
    - Feature extraction (power spectral density)
    """
    
    def __init__(self, 
                 l_freq=8.0, 
                 h_freq=30.0, 
                 notch_freq=50.0,
                 epoch_tmin=0.0, 
                 epoch_tmax=4.0,
                 baseline=None,
                 picks='eeg'):
        """
        Initialize EEG preprocessor
        
        Parameters:
        -----------
        l_freq : float
            Low frequency for bandpass filter (Hz)
        h_freq : float  
            High frequency for bandpass filter (Hz)
        notch_freq : float
            Frequency for notch filter (Hz) - removes power line noise
        epoch_tmin : float
            Start time for epochs relative to event (seconds)
        epoch_tmax : float
            End time for epochs relative to event (seconds)
        baseline : tuple or None
            Baseline correction period (start, end) in seconds
        picks : str or list
            Channels to include ('eeg', 'all', or list of channel names)
        """
        self.l_freq = l_freq
        self.h_freq = h_freq
        self.notch_freq = notch_freq
        self.epoch_tmin = epoch_tmin
        self.epoch_tmax = epoch_tmax
        self.baseline = baseline
        self.picks = picks
        
        # Will be set during fit
        self.ch_names_ = None
        self.sfreq_ = None
        self.event_id_ = None
        
    def load_raw_data(self, subject_id, run, data_dir="data"):
        """Load raw EEG data for a subject and run"""
        raw_fname = mne.datasets.eegbci.load_data(
            subjects=[subject_id], 
            runs=[run], 
            path=data_dir,
            update_path=False
        )[0]
        
        raw = mne.io.read_raw_edf(raw_fname, preload=True, verbose=False)
        
        # Set montage with error handling
        montage = mne.channels.make_standard_montage('standard_1005')
        try:
            raw.set_montage(montage, verbose=False)
        except ValueError:
            raw.set_montage(montage, on_missing='ignore', verbose=False)
            
        return raw
    
    def filter_raw(self, raw):
        """Apply filtering to raw data"""
        # Copy to avoid modifying original
        raw_filtered = raw.copy()
        
        # Bandpass filter
        raw_filtered.filter(
            l_freq=self.l_freq, 
            h_freq=self.h_freq, 
            picks=self.picks,
            verbose=False
        )
        
        # Notch filter for power line noise
        if self.notch_freq is not None:
            raw_filtered.notch_filter(
                freqs=self.notch_freq,
                picks=self.picks,
                verbose=False
            )
            
        return raw_filtered
    
    def create_epochs(self, raw, events, event_id):
        """Create epochs from raw data and events"""
        epochs = Epochs(
            raw,
            events,
            event_id=event_id,
            tmin=self.epoch_tmin,
            tmax=self.epoch_tmax,
            baseline=self.baseline,
            picks=self.picks,
            preload=True,
            verbose=False
        )
        
        return epochs
    
    def extract_features(self, epochs):
        """
        Extract features from epochs
        
        For now, we'll extract power spectral density features
        which are commonly used in motor imagery BCI
        """
        # Get epoch data: (n_epochs, n_channels, n_times)
        data = epochs.get_data()
        n_epochs, n_channels, n_times = data.shape
        
        # Extract PSD features for each epoch
        features = []
        
        for epoch_data in data:
            # Compute PSD for each channel
            freqs, psd = psd_array_welch(
                epoch_data,
                sfreq=epochs.info['sfreq'],
                fmin=self.l_freq,
                fmax=self.h_freq,
                verbose=False
            )
            
            # Flatten PSD across channels and frequencies
            # Shape: (n_channels * n_freqs,)
            psd_features = psd.flatten()
            features.append(psd_features)
        
        # Convert to array: (n_epochs, n_features)
        features = np.array(features)
        
        return features, freqs
    
    def fit(self, X=None, y=None):
        """
        Fit the preprocessor (mainly for sklearn compatibility)
        In practice, we'll fit on the first dataset to learn parameters
        """
        return self
    
    def transform(self, X):
        """
        Transform method for sklearn compatibility
        X should be a list of (raw, events, event_id) tuples
        """
        if not isinstance(X, list):
            X = [X]
            
        all_features = []
        all_labels = []
        
        for raw, events, event_id in X:
            # Filter raw data
            raw_filtered = self.filter_raw(raw)
            
            # Create epochs
            epochs = self.create_epochs(raw_filtered, events, event_id)
            
            # Extract features
            features, _ = self.extract_features(epochs)
            
            # Get labels
            labels = epochs.events[:, -1]  # Last column contains event codes
            
            all_features.append(features)
            all_labels.append(labels)
        
        # Concatenate all features and labels
        X_features = np.vstack(all_features)
        y_labels = np.hstack(all_labels)
        
        return X_features, y_labels
    
    def fit_transform(self, X, y=None):
        """Fit and transform in one step"""
        return self.fit(X, y).transform(X)


def visualize_preprocessing_steps(subject_id=1, run=4, data_dir="data"):
    """
    Visualize the preprocessing steps
    """
    print(f"Visualizing preprocessing for subject {subject_id}, run {run}")
    
    # Initialize preprocessor
    preprocessor = EEGPreprocessor()
    
    # Load raw data
    raw = preprocessor.load_raw_data(subject_id, run, data_dir)
    print(f"Loaded raw data: {raw.get_data().shape}")
    
    # Get events
    events, event_id = mne.events_from_annotations(raw, verbose=False)
    print(f"Found {len(events)} events: {event_id}")
    
    # Filter data
    raw_filtered = preprocessor.filter_raw(raw)
    print("Applied filtering")
    
    # Create epochs
    epochs = preprocessor.create_epochs(raw_filtered, events, event_id)
    print(f"Created {len(epochs)} epochs")
    
    # Extract features
    features, freqs = preprocessor.extract_features(epochs)
    print(f"Extracted features: {features.shape}")
    
    # Visualize comparison
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Raw data
    channel_idx = raw.ch_names.index('C3..')
    time_slice = slice(8000, 10000)  # 2 seconds of data
    times = raw.times[time_slice]
    
    axes[0, 0].plot(times, raw.get_data()[channel_idx, time_slice])
    axes[0, 0].set_title('Raw EEG (C3)')
    axes[0, 0].set_xlabel('Time (s)')
    axes[0, 0].set_ylabel('Amplitude (V)')
    
    # Filtered data
    axes[0, 1].plot(times, raw_filtered.get_data()[channel_idx, time_slice])
    axes[0, 1].set_title('Filtered EEG (C3)')
    axes[0, 1].set_xlabel('Time (s)')
    axes[0, 1].set_ylabel('Amplitude (V)')
    
    # Epoch average
    epoch_avg = epochs.get_data().mean(axis=0)
    axes[1, 0].plot(epochs.times, epoch_avg[channel_idx, :])
    axes[1, 0].set_title('Average Epoch (C3)')
    axes[1, 0].set_xlabel('Time (s)')
    axes[1, 0].set_ylabel('Amplitude (V)')
    
    # Feature distribution
    axes[1, 1].hist(features[:, 0], bins=20, alpha=0.7)
    axes[1, 1].set_title('Feature Distribution (First Feature)')
    axes[1, 1].set_xlabel('Feature Value')
    axes[1, 1].set_ylabel('Count')
    
    plt.tight_layout()
    plt.show()
    
    return preprocessor, raw, events, event_id, features


if __name__ == "__main__":
    # Test preprocessing pipeline
    print("=== EEG Preprocessing Pipeline Test ===")
    
    preprocessor, raw, events, event_id, features = visualize_preprocessing_steps()
    
    print(f"\nPreprocessing complete!")
    print(f"Features shape: {features.shape}")
    print(f"Event types: {event_id}")
