"""
EEG Data Visualization for Total Perspective Vortex
Comprehensive visualization tools for brain signals and BCI analysis
"""

import numpy as np # type: ignore
import matplotlib.pyplot as plt # type: ignore
import seaborn as sns # type: ignore
import mne # type: ignore
from mne.time_frequency import psd_array_welch # type: ignore
from sklearn.decomposition import PCA # type: ignore
from sklearn.manifold import TSNE # type: ignore
import warnings
warnings.filterwarnings('ignore')

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config import PREPROCESSING_CONFIG # type: ignore


class EEGVisualizer:
    """
    Comprehensive EEG visualization toolkit for brain-computer interface analysis
    """
    
    def __init__(self, figsize=(15, 10)):
        """Initialize visualizer with default figure size"""
        self.figsize = figsize
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def plot_raw_eeg(self, raw, duration=10, start=60, channels=None, title="Raw EEG Signals"):
        """
        Plot raw EEG signals
        
        Parameters:
        -----------
        raw : mne.Raw
            Raw EEG data
        duration : float
            Duration to plot in seconds
        start : float
            Start time in seconds
        channels : list or None
            Specific channels to plot
        """
        if channels is None:
            # Select motor cortex channels
            motor_channels = ['C3..', 'Cz..', 'C4..', 'Cp3.', 'Cpz.', 'Cp4.']
            channels = [ch for ch in motor_channels if ch in raw.ch_names][:6]
        
        # Get data
        data, times = raw[channels, int(start * raw.info['sfreq']):int((start + duration) * raw.info['sfreq'])]
        times = times + start
        
        # Create plot
        fig, axes = plt.subplots(len(channels), 1, figsize=self.figsize, sharex=True)
        if len(channels) == 1:
            axes = [axes]
        
        for i, (ch_data, ch_name) in enumerate(zip(data, channels)):
            axes[i].plot(times, ch_data * 1e6, 'b-', linewidth=0.8)  # Convert to µV
            axes[i].set_ylabel(f'{ch_name}\n(µV)', fontsize=10)
            axes[i].grid(True, alpha=0.3)
            axes[i].set_ylim(np.percentile(ch_data * 1e6, [1, 99]))
        
        axes[-1].set_xlabel('Time (s)')
        plt.suptitle(f'{title} - {duration}s from {start}s', fontsize=14, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def plot_power_spectrum(self, raw, channels=None, title="Power Spectral Density"):
        """
        Plot power spectral density for EEG channels
        """
        if channels is None:
            motor_channels = ['C3..', 'Cz..', 'C4..', 'Cp3.', 'Cpz.', 'Cp4.']
            channels = [ch for ch in motor_channels if ch in raw.ch_names][:4]
        
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        axes = axes.flatten()
        
        for i, ch_name in enumerate(channels[:4]):
            if i >= len(axes):
                break
                
            # Get channel data
            ch_idx = raw.ch_names.index(ch_name)
            data = raw.get_data()[ch_idx, :]
            
            # Compute PSD
            freqs, psd = psd_array_welch(
                data[np.newaxis, :], 
                sfreq=raw.info['sfreq'],
                fmin=1, fmax=50,
                verbose=False
            )
            
            # Plot
            axes[i].semilogy(freqs, psd[0], 'b-', linewidth=1.5)
            axes[i].set_xlabel('Frequency (Hz)')
            axes[i].set_ylabel('Power (V²/Hz)')
            axes[i].set_title(f'{ch_name}')
            axes[i].grid(True, alpha=0.3)
            
            # Highlight frequency bands
            axes[i].axvspan(8, 12, alpha=0.2, color='green', label='Alpha')
            axes[i].axvspan(13, 30, alpha=0.2, color='blue', label='Beta')
            if i == 0:
                axes[i].legend()
        
        plt.suptitle(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def plot_preprocessing_comparison(self, raw_original, raw_filtered, channel='C3..'):
        """
        Compare raw vs filtered signals
        """
        fig, axes = plt.subplots(3, 2, figsize=self.figsize)
        
        # Time domain comparison
        duration = 5
        start = 60
        
        for i, (raw, label) in enumerate([(raw_original, 'Original'), (raw_filtered, 'Filtered')]):
            if channel not in raw.ch_names:
                continue
                
            # Time series
            data, times = raw[channel, int(start * raw.info['sfreq']):int((start + duration) * raw.info['sfreq'])]
            times = times + start
            
            axes[0, i].plot(times, data[0] * 1e6, 'b-', linewidth=1)
            axes[0, i].set_title(f'{label} Signal - {channel}')
            axes[0, i].set_ylabel('Amplitude (µV)')
            axes[0, i].grid(True, alpha=0.3)
            
            # Power spectrum
            ch_idx = raw.ch_names.index(channel)
            data_full = raw.get_data()[ch_idx, :]
            freqs, psd = psd_array_welch(
                data_full[np.newaxis, :], 
                sfreq=raw.info['sfreq'],
                fmin=1, fmax=50,
                verbose=False
            )
            
            axes[1, i].semilogy(freqs, psd[0], 'r-', linewidth=1.5)
            axes[1, i].set_title(f'{label} PSD - {channel}')
            axes[1, i].set_ylabel('Power (V²/Hz)')
            axes[1, i].grid(True, alpha=0.3)
            
            # Spectrogram
            try:
                f, t, Sxx = plt.mlab.specgram(data_full, Fs=raw.info['sfreq'], NFFT=256, noverlap=128)
                if Sxx.ndim == 2:  # Ensure 2D array
                    im = axes[2, i].imshow(10 * np.log10(Sxx + 1e-10), aspect='auto', origin='lower',
                                         extent=[t[0], t[-1], f[0], f[-1]])
                    axes[2, i].set_title(f'{label} Spectrogram - {channel}')
                    axes[2, i].set_ylabel('Frequency (Hz)')
                    axes[2, i].set_xlabel('Time (s)')
                    axes[2, i].set_ylim([1, 50])

                    if i == 1:
                        plt.colorbar(im, ax=axes[2, i], label='Power (dB)')
                else:
                    axes[2, i].text(0.5, 0.5, 'Spectrogram\ndata error',
                                   ha='center', va='center', transform=axes[2, i].transAxes)
                    axes[2, i].set_title(f'{label} Spectrogram - {channel}')
            except Exception as e:
                axes[2, i].text(0.5, 0.5, f'Spectrogram\nerror: {str(e)[:20]}...',
                               ha='center', va='center', transform=axes[2, i].transAxes)
                axes[2, i].set_title(f'{label} Spectrogram - {channel}')
        
        plt.tight_layout()
        return fig
    
    def plot_epochs_overview(self, epochs, title="Epochs Overview"):
        """
        Plot overview of epoched data
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # Get data
        data = epochs.get_data()  # (n_epochs, n_channels, n_times)
        events = epochs.events
        
        # Plot 1: Average epochs by class
        unique_events = np.unique(events[:, -1])
        colors = ['blue', 'red', 'green', 'orange']
        
        for i, event_code in enumerate(unique_events):
            mask = events[:, -1] == event_code
            avg_data = data[mask].mean(axis=0)
            
            # Plot average for motor cortex channel
            if 'C3..' in epochs.ch_names:
                ch_idx = epochs.ch_names.index('C3..')
                axes[0, 0].plot(epochs.times, avg_data[ch_idx] * 1e6, 
                              color=colors[i % len(colors)], 
                              label=f'Event {event_code}', linewidth=2)
        
        axes[0, 0].set_title('Average Epochs by Class (C3)')
        axes[0, 0].set_xlabel('Time (s)')
        axes[0, 0].set_ylabel('Amplitude (µV)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Plot 2: Epoch count by class
        event_counts = [np.sum(events[:, -1] == code) for code in unique_events]
        axes[0, 1].bar(range(len(unique_events)), event_counts, 
                      color=[colors[i % len(colors)] for i in range(len(unique_events))])
        axes[0, 1].set_title('Epochs per Class')
        axes[0, 1].set_xlabel('Event Code')
        axes[0, 1].set_ylabel('Number of Epochs')
        axes[0, 1].set_xticks(range(len(unique_events)))
        axes[0, 1].set_xticklabels(unique_events)
        
        # Plot 3: Channel variance
        channel_var = np.var(data, axis=(0, 2))
        motor_channels = ['C3..', 'Cz..', 'C4..', 'Cp3.', 'Cpz.', 'Cp4.']
        motor_indices = [i for i, ch in enumerate(epochs.ch_names) if ch in motor_channels]
        
        if motor_indices:
            axes[1, 0].bar(range(len(motor_indices)), channel_var[motor_indices])
            axes[1, 0].set_title('Channel Variance (Motor Cortex)')
            axes[1, 0].set_xlabel('Channel')
            axes[1, 0].set_ylabel('Variance')
            axes[1, 0].set_xticks(range(len(motor_indices)))
            axes[1, 0].set_xticklabels([epochs.ch_names[i] for i in motor_indices], rotation=45)
        
        # Plot 4: Epoch quality (peak-to-peak amplitude)
        epoch_quality = np.max(data, axis=2) - np.min(data, axis=2)
        epoch_quality_mean = np.mean(epoch_quality, axis=1)
        
        axes[1, 1].hist(epoch_quality_mean * 1e6, bins=20, alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('Epoch Quality Distribution')
        axes[1, 1].set_xlabel('Peak-to-Peak Amplitude (µV)')
        axes[1, 1].set_ylabel('Count')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def plot_csp_patterns(self, csp, info=None, title="CSP Spatial Patterns"):
        """
        Visualize CSP spatial patterns
        """
        if csp.patterns_ is None:
            raise ValueError("CSP must be fitted before plotting patterns")
        
        n_components = min(6, csp.patterns_.shape[1])  # Show max 6 components
        
        fig, axes = plt.subplots(2, 3, figsize=self.figsize)
        axes = axes.flatten()
        
        for i in range(n_components):
            pattern = csp.patterns_[:, i]
            eigenval = csp.eigenvalues_[i]
            
            # Create topographic-like plot
            motor_channels = ['C3..', 'Cz..', 'C4..', 'Cp3.', 'Cpz.', 'Cp4.']
            if info and 'ch_names' in info:
                motor_indices = [j for j, ch in enumerate(info['ch_names']) if ch in motor_channels]
                # Ensure indices are within pattern bounds
                motor_indices = [j for j in motor_indices if j < len(pattern)]
            else:
                motor_indices = list(range(min(6, len(pattern))))

            if motor_indices:
                motor_pattern = pattern[motor_indices]
                if info and 'ch_names' in info:
                    motor_names = [info['ch_names'][j] for j in motor_indices]
                else:
                    motor_names = [f'Ch{j}' for j in motor_indices]
                
                # Bar plot of weights
                colors = ['red' if w < 0 else 'blue' for w in motor_pattern]
                bars = axes[i].bar(range(len(motor_pattern)), motor_pattern, color=colors, alpha=0.7)
                axes[i].set_title(f'CSP {i+1} (λ={eigenval:.3f})')
                axes[i].set_ylabel('Weight')
                axes[i].set_xticks(range(len(motor_pattern)))
                axes[i].set_xticklabels(motor_names, rotation=45)
                axes[i].grid(True, alpha=0.3)
                axes[i].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Hide unused subplots
        for i in range(n_components, len(axes)):
            axes[i].set_visible(False)
        
        plt.suptitle(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def plot_classification_results(self, y_true, y_pred, y_proba=None, title="Classification Results"):
        """
        Visualize classification results
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # Confusion matrix
        from sklearn.metrics import confusion_matrix, classification_report
        cm = confusion_matrix(y_true, y_pred)
        
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, 0])
        axes[0, 0].set_title('Confusion Matrix')
        axes[0, 0].set_xlabel('Predicted')
        axes[0, 0].set_ylabel('True')
        
        # Prediction timeline
        correct = y_true == y_pred
        axes[0, 1].scatter(range(len(y_true)), y_true, c='blue', alpha=0.6, label='True', s=50)
        axes[0, 1].scatter(range(len(y_pred)), y_pred, c='red', alpha=0.6, label='Predicted', s=30, marker='x')
        axes[0, 1].set_title('Prediction Timeline')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Class')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Accuracy over time
        window_size = max(5, len(y_true) // 10)
        rolling_acc = []
        for i in range(window_size, len(y_true) + 1):
            window_acc = np.mean(y_true[i-window_size:i] == y_pred[i-window_size:i])
            rolling_acc.append(window_acc)
        
        axes[1, 0].plot(range(window_size, len(y_true) + 1), rolling_acc, 'g-', linewidth=2)
        axes[1, 0].axhline(y=0.6, color='red', linestyle='--', label='60% Target')
        axes[1, 0].set_title(f'Rolling Accuracy (window={window_size})')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Accuracy')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_ylim([0, 1])
        
        # Prediction confidence (if available)
        if y_proba is not None:
            max_proba = np.max(y_proba, axis=1)
            axes[1, 1].hist(max_proba, bins=20, alpha=0.7, edgecolor='black')
            axes[1, 1].axvline(x=np.mean(max_proba), color='red', linestyle='--', 
                             label=f'Mean: {np.mean(max_proba):.3f}')
            axes[1, 1].set_title('Prediction Confidence')
            axes[1, 1].set_xlabel('Max Probability')
            axes[1, 1].set_ylabel('Count')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, 'No probability\ndata available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Prediction Confidence')
        
        plt.suptitle(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def plot_feature_space(self, X_features, y_labels, title="Feature Space Visualization"):
        """
        Visualize high-dimensional features in 2D using t-SNE
        """
        fig, axes = plt.subplots(1, 2, figsize=self.figsize)
        
        # PCA visualization
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_features)
        
        unique_labels = np.unique(y_labels)
        colors = ['blue', 'red', 'green', 'orange']
        
        for i, label in enumerate(unique_labels):
            mask = y_labels == label
            axes[0].scatter(X_pca[mask, 0], X_pca[mask, 1], 
                          c=colors[i % len(colors)], alpha=0.6, 
                          label=f'Class {label}', s=30)
        
        axes[0].set_title(f'PCA Feature Space\n(Explained variance: {pca.explained_variance_ratio_.sum():.2%})')
        axes[0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%})')
        axes[0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%})')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # t-SNE visualization (if not too many samples)
        if len(X_features) <= 1000:
            tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_features)//4))
            X_tsne = tsne.fit_transform(X_features)
            
            for i, label in enumerate(unique_labels):
                mask = y_labels == label
                axes[1].scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                              c=colors[i % len(colors)], alpha=0.6, 
                              label=f'Class {label}', s=30)
            
            axes[1].set_title('t-SNE Feature Space')
            axes[1].set_xlabel('t-SNE 1')
            axes[1].set_ylabel('t-SNE 2')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)
        else:
            axes[1].text(0.5, 0.5, f'Too many samples\n({len(X_features)}) for t-SNE\nvisualization', 
                        ha='center', va='center', transform=axes[1].transAxes)
            axes[1].set_title('t-SNE Feature Space')
        
        plt.suptitle(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        return fig


def save_figure(fig, filename, dpi=300):
    """Save figure with high quality"""
    output_dir = Path("visualizations")
    output_dir.mkdir(exist_ok=True)
    
    filepath = output_dir / filename
    fig.savefig(filepath, dpi=dpi, bbox_inches='tight', facecolor='white')
    print(f"📊 Visualization saved: {filepath}")
    return filepath
