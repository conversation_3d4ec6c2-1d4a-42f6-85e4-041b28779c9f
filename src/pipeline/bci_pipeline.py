"""
Complete BCI Pipeline for Motor Imagery Classification
Combines preprocessing, CSP, and classification in sklearn pipeline
"""

import numpy as np # type: ignore
import mne # type: ignore
from sklearn.pipeline import Pipeline # type: ignore
from sklearn.model_selection import cross_val_score, train_test_split, StratifiedKFold # type: ignore
from sklearn.linear_model import LogisticRegression # type: ignore
from sklearn.svm import SVC # type: ignore
from sklearn.ensemble import RandomForestClassifier # type: ignore
from sklearn.metrics import classification_report, confusion_matrix # type: ignore
from sklearn.base import BaseEstimator, TransformerMixin # type: ignore
import matplotlib.pyplot as plt # type: ignore
import seaborn as sns # type: ignore

# Import our custom modules
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dimensionality.csp import CSP
from config import get_data_path


class EEGDataLoader(BaseEstimator, TransformerMixin):
    """
    Custom transformer to load and preprocess EEG data for sklearn pipeline
    """
    
    def __init__(self, 
                 l_freq=8.0, 
                 h_freq=30.0, 
                 epoch_tmin=0.0, 
                 epoch_tmax=4.0,
                 baseline=None,
                 picks='eeg'):
        self.l_freq = l_freq
        self.h_freq = h_freq
        self.epoch_tmin = epoch_tmin
        self.epoch_tmax = epoch_tmax
        self.baseline = baseline
        self.picks = picks
        
    def fit(self, X, y=None):
        return self
    
    def transform(self, X):
        """
        Transform raw EEG data to epoched format
        X should be list of (subject_id, run) tuples
        """
        all_epochs = []
        all_labels = []
        
        for subject_id, run in X:
            # Load raw data
            raw = self._load_raw_data(subject_id, run)
            
            # Filter
            raw_filtered = self._filter_raw(raw)
            
            # Get events
            events, event_id = mne.events_from_annotations(raw_filtered, verbose=False)
            
            # Create epochs
            epochs = self._create_epochs(raw_filtered, events, event_id)
            
            # Get data and labels
            epoch_data = epochs.get_data()  # (n_epochs, n_channels, n_times)
            labels = epochs.events[:, -1]  # Event codes
            
            all_epochs.append(epoch_data)
            all_labels.append(labels)
        
        # Concatenate all data
        X_epochs = np.vstack(all_epochs)
        y_labels = np.hstack(all_labels)
        
        return X_epochs, y_labels
    
    def _load_raw_data(self, subject_id, run, data_dir=None):
        """Load raw EEG data"""
        if data_dir is None:
            data_dir = get_data_path()

        raw_fname = mne.datasets.eegbci.load_data(
            subjects=[subject_id],
            runs=[run],
            path=data_dir,
            update_path=False
        )[0]
        
        raw = mne.io.read_raw_edf(raw_fname, preload=True, verbose=False)
        
        # Set montage
        montage = mne.channels.make_standard_montage('standard_1005')
        try:
            raw.set_montage(montage, verbose=False)
        except ValueError:
            raw.set_montage(montage, on_missing='ignore', verbose=False)
            
        return raw
    
    def _filter_raw(self, raw):
        """Apply filtering"""
        raw_filtered = raw.copy()
        raw_filtered.filter(
            l_freq=self.l_freq, 
            h_freq=self.h_freq, 
            picks=self.picks,
            verbose=False
        )
        raw_filtered.notch_filter(freqs=50.0, picks=self.picks, verbose=False)
        return raw_filtered
    
    def _create_epochs(self, raw, events, event_id):
        """Create epochs"""
        epochs = mne.Epochs(
            raw,
            events,
            event_id=event_id,
            tmin=self.epoch_tmin,
            tmax=self.epoch_tmax,
            baseline=self.baseline,
            picks=self.picks,
            preload=True,
            verbose=False
        )
        return epochs


class BCIPipeline:
    """
    Complete BCI pipeline for motor imagery classification
    """
    
    def __init__(self, 
                 n_csp_components=6,
                 classifier='lr',
                 l_freq=8.0,
                 h_freq=30.0):
        """
        Initialize BCI pipeline
        
        Parameters:
        -----------
        n_csp_components : int
            Number of CSP components
        classifier : str
            Classifier type: 'lr', 'svm', 'rf'
        l_freq, h_freq : float
            Frequency band for filtering
        """
        self.n_csp_components = n_csp_components
        self.classifier = classifier
        self.l_freq = l_freq
        self.h_freq = h_freq
        
        # Build pipeline
        self.pipeline = self._build_pipeline()
        
    def _build_pipeline(self):
        """Build sklearn pipeline"""
        
        # Choose classifier
        if self.classifier == 'lr':
            clf = LogisticRegression(random_state=42, max_iter=1000)
        elif self.classifier == 'svm':
            clf = SVC(random_state=42, probability=True)
        elif self.classifier == 'rf':
            clf = RandomForestClassifier(random_state=42, n_estimators=100)
        else:
            raise ValueError(f"Unknown classifier: {self.classifier}")
        
        # Create pipeline
        pipeline = Pipeline([
            ('loader', EEGDataLoader(l_freq=self.l_freq, h_freq=self.h_freq)),
            ('csp', CSP(n_components=self.n_csp_components, log=True)),
            ('classifier', clf)
        ])
        
        return pipeline
    
    def prepare_data(self, subject_ids, runs):
        """
        Prepare data for training/testing
        
        Parameters:
        -----------
        subject_ids : list
            List of subject IDs
        runs : list
            List of run numbers
            
        Returns:
        --------
        X : list
            List of (subject_id, run) tuples
        """
        X = []
        for subject_id in subject_ids:
            for run in runs:
                X.append((subject_id, run))
        return X
    
    def train_and_evaluate(self, subject_ids, train_runs, test_runs=None, cv_folds=5):
        """
        Train and evaluate the BCI pipeline
        
        Parameters:
        -----------
        subject_ids : list
            Subject IDs to use
        train_runs : list
            Run numbers for training
        test_runs : list or None
            Run numbers for testing (if None, use cross-validation)
        cv_folds : int
            Number of cross-validation folds
            
        Returns:
        --------
        results : dict
            Training and evaluation results
        """
        print(f"Training BCI pipeline for subjects {subject_ids}")
        print(f"Training runs: {train_runs}")
        
        # Prepare training data
        X_train = self.prepare_data(subject_ids, train_runs)
        
        # Load and transform training data
        loader = EEGDataLoader(l_freq=self.l_freq, h_freq=self.h_freq)
        X_epochs, y_labels = loader.transform(X_train)
        
        print(f"Training data shape: {X_epochs.shape}")
        print(f"Labels shape: {y_labels.shape}")
        print(f"Unique labels: {np.unique(y_labels)}")
        
        # Filter for binary classification (T1 vs T2)
        # T1 (left/right fist) vs T2 (imagine left/right fist)
        mask = (y_labels == 2) | (y_labels == 3)  # T1 and T2 events
        X_binary = X_epochs[mask]
        y_binary = y_labels[mask]
        
        # Convert to binary labels (0 and 1)
        y_binary = (y_binary == 3).astype(int)  # T2 = 1, T1 = 0
        
        print(f"Binary classification data: {X_binary.shape}")
        print(f"Class distribution: {np.bincount(y_binary)}")
        
        if len(np.unique(y_binary)) < 2:
            raise ValueError("Need at least 2 classes for classification")
        
        # Cross-validation
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        # Create CSP + Classifier pipeline (without data loader)
        csp_clf_pipeline = Pipeline([
            ('csp', CSP(n_components=self.n_csp_components, log=True)),
            ('classifier', self.pipeline.named_steps['classifier'])
        ])
        
        # Perform cross-validation
        cv_scores = cross_val_score(
            csp_clf_pipeline, 
            X_binary, 
            y_binary, 
            cv=cv, 
            scoring='accuracy'
        )
        
        print(f"Cross-validation scores: {cv_scores}")
        print(f"Mean CV accuracy: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        # Train final model on all data
        csp_clf_pipeline.fit(X_binary, y_binary)
        
        # Test on separate data if provided
        test_accuracy = None
        if test_runs is not None:
            print(f"Testing on runs: {test_runs}")
            X_test = self.prepare_data(subject_ids, test_runs)
            X_test_epochs, y_test_labels = loader.transform(X_test)
            
            # Filter for binary classification
            test_mask = (y_test_labels == 2) | (y_test_labels == 3)
            X_test_binary = X_test_epochs[test_mask]
            y_test_binary = y_test_labels[test_mask]
            y_test_binary = (y_test_binary == 3).astype(int)
            
            if len(y_test_binary) > 0:
                test_accuracy = csp_clf_pipeline.score(X_test_binary, y_test_binary)
                print(f"Test accuracy: {test_accuracy:.4f}")
        
        # Store trained pipeline
        self.trained_pipeline = csp_clf_pipeline
        
        results = {
            'cv_scores': cv_scores,
            'mean_cv_accuracy': cv_scores.mean(),
            'std_cv_accuracy': cv_scores.std(),
            'test_accuracy': test_accuracy,
            'n_train_samples': len(y_binary),
            'class_distribution': np.bincount(y_binary)
        }
        
        return results
    
    def predict(self, subject_ids, runs):
        """Make predictions on new data"""
        if not hasattr(self, 'trained_pipeline'):
            raise ValueError("Pipeline must be trained before prediction")
        
        # Prepare data
        X = self.prepare_data(subject_ids, runs)
        
        # Load and transform data
        loader = EEGDataLoader(l_freq=self.l_freq, h_freq=self.h_freq)
        X_epochs, y_labels = loader.transform(X)
        
        # Filter for binary classification
        mask = (y_labels == 2) | (y_labels == 3)
        X_binary = X_epochs[mask]
        y_true = y_labels[mask]
        y_true_binary = (y_true == 3).astype(int)
        
        # Make predictions
        y_pred = self.trained_pipeline.predict(X_binary)
        y_pred_proba = self.trained_pipeline.predict_proba(X_binary)
        
        return y_pred, y_pred_proba, y_true_binary


def test_bci_pipeline():
    """Test the complete BCI pipeline"""
    print("=== Testing Complete BCI Pipeline ===")
    
    # Initialize pipeline
    bci = BCIPipeline(
        n_csp_components=6,
        classifier='lr',
        l_freq=8.0,
        h_freq=30.0
    )
    
    # Test with subject 1, motor imagery runs
    subject_ids = [1]
    train_runs = [4, 8, 12]  # Motor imagery runs
    test_runs = [6, 10, 14]  # Different motor imagery runs
    
    try:
        # Train and evaluate
        results = bci.train_and_evaluate(
            subject_ids=subject_ids,
            train_runs=train_runs,
            test_runs=test_runs,
            cv_folds=3
        )
        
        print("\n=== Results ===")
        for key, value in results.items():
            print(f"{key}: {value}")
        
        # Test prediction
        print("\n=== Testing Prediction ===")
        y_pred, y_pred_proba, y_true = bci.predict(subject_ids, test_runs)
        
        print(f"Predictions shape: {y_pred.shape}")
        print(f"True labels shape: {y_true.shape}")
        
        if len(y_pred) > 0:
            accuracy = np.mean(y_pred == y_true)
            print(f"Prediction accuracy: {accuracy:.4f}")
        
        return bci, results
        
    except Exception as e:
        print(f"Error in pipeline test: {e}")
        return None, None


if __name__ == "__main__":
    bci, results = test_bci_pipeline()
