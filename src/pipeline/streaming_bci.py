"""
Real-time Streaming BCI System
Simulates data stream processing with <2s prediction delay
"""

import numpy as np # type: ignore
import time
import mne # type: ignore
from sklearn.base import BaseEstimator, TransformerMixin # type: ignore
import pickle
from pathlib import Path

import sys
sys.path.append(str(Path(__file__).parent.parent))

from dimensionality.csp import CSP
from pipeline.improved_bci import ImprovedEEGPreprocessor
from config import get_models_path


class StreamingBCI:
    """
    Real-time streaming BCI system that processes data chunks
    and makes predictions within 2-second delay
    """
    
    def __init__(self, model_path=None):
        """
        Initialize streaming BCI system
        
        Parameters:
        -----------
        model_path : str or None
            Path to trained model file
        """
        self.model_path = model_path
        self.trained_model = None
        self.preprocessor = None
        self.buffer = []
        self.buffer_size = 4.0  # 4 seconds of data
        self.prediction_window = 4.0  # Predict on 4s windows
        self.sfreq = 160.0  # Sampling frequency
        
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
    
    def load_model(self, model_path):
        """Load trained BCI model"""
        with open(model_path, 'rb') as f:
            bci_pipeline = pickle.load(f)
            self.trained_model = bci_pipeline.trained_pipeline
            
        # Initialize preprocessor
        self.preprocessor = ImprovedEEGPreprocessor()
        print(f"Loaded model from {model_path}")
    
    def simulate_data_stream(self, subject_id, run, chunk_duration=0.5):
        """
        Simulate real-time data stream from EEG file
        
        Parameters:
        -----------
        subject_id : int
            Subject ID
        run : int
            Run number
        chunk_duration : float
            Duration of each data chunk in seconds
        
        Yields:
        -------
        chunk : array
            Data chunk of shape (n_channels, n_samples)
        """
        # Load raw data
        raw = self.preprocessor._load_raw_data(subject_id, run)
        raw_filtered = self.preprocessor._improved_filter(raw)
        
        # Get data
        data = raw_filtered.get_data()  # Shape: (n_channels, n_samples)
        n_channels, n_samples = data.shape
        
        # Calculate chunk size
        chunk_samples = int(chunk_duration * self.sfreq)
        
        print(f"Starting data stream simulation...")
        print(f"Data shape: {data.shape}")
        print(f"Chunk duration: {chunk_duration}s ({chunk_samples} samples)")
        print(f"Total duration: {n_samples / self.sfreq:.1f}s")
        
        # Stream data in chunks
        for start_idx in range(0, n_samples - chunk_samples, chunk_samples):
            end_idx = start_idx + chunk_samples
            chunk = data[:, start_idx:end_idx]
            
            # Simulate real-time delay
            time.sleep(0.1)  # Small delay to simulate real-time
            
            yield chunk, start_idx / self.sfreq
    
    def update_buffer(self, chunk):
        """
        Update data buffer with new chunk
        
        Parameters:
        -----------
        chunk : array
            New data chunk of shape (n_channels, n_samples)
        """
        self.buffer.append(chunk)
        
        # Calculate total buffer duration
        total_samples = sum(c.shape[1] for c in self.buffer)
        buffer_duration = total_samples / self.sfreq
        
        # Remove old data if buffer is too long
        while buffer_duration > self.buffer_size:
            removed_chunk = self.buffer.pop(0)
            total_samples -= removed_chunk.shape[1]
            buffer_duration = total_samples / self.sfreq
    
    def get_prediction_window(self):
        """
        Get current prediction window from buffer
        
        Returns:
        --------
        window : array or None
            Data window of shape (n_channels, n_samples) or None if insufficient data
        """
        if not self.buffer:
            return None
        
        # Concatenate buffer data
        buffer_data = np.hstack(self.buffer)
        n_channels, n_samples = buffer_data.shape
        
        # Check if we have enough data for prediction
        required_samples = int(self.prediction_window * self.sfreq)
        
        if n_samples < required_samples:
            return None
        
        # Return last prediction_window seconds
        window = buffer_data[:, -required_samples:]
        return window
    
    def predict_from_window(self, window):
        """
        Make prediction from data window
        
        Parameters:
        -----------
        window : array
            Data window of shape (n_channels, n_samples)
            
        Returns:
        --------
        prediction : int
            Predicted class (0 or 1)
        probability : float
            Prediction probability
        """
        if self.trained_model is None:
            raise ValueError("No trained model loaded")
        
        # Reshape for prediction (add epoch dimension)
        epoch_data = window[np.newaxis, :, :]  # Shape: (1, n_channels, n_samples)
        
        # Make prediction
        start_time = time.time()
        
        prediction = self.trained_model.predict(epoch_data)[0]
        probabilities = self.trained_model.predict_proba(epoch_data)[0]
        
        prediction_time = time.time() - start_time
        
        return prediction, probabilities.max(), prediction_time
    
    def run_streaming_session(self, subject_id, run, session_duration=30.0):
        """
        Run a streaming BCI session
        
        Parameters:
        -----------
        subject_id : int
            Subject ID
        run : int
            Run number
        session_duration : float
            Duration of session in seconds
        """
        if self.trained_model is None:
            print("Error: No trained model loaded")
            return
        
        print(f"\n{'='*50}")
        print(f"STREAMING BCI SESSION")
        print(f"Subject: {subject_id}, Run: {run}")
        print(f"Session duration: {session_duration}s")
        print(f"{'='*50}")
        
        predictions = []
        prediction_times = []
        
        start_time = time.time()
        
        # Start data stream
        for chunk, timestamp in self.simulate_data_stream(subject_id, run):
            # Check if session time exceeded
            if time.time() - start_time > session_duration:
                break
            
            # Update buffer
            self.update_buffer(chunk)
            
            # Try to make prediction
            window = self.get_prediction_window()
            
            if window is not None:
                try:
                    pred, prob, pred_time = self.predict_from_window(window)
                    
                    predictions.append(pred)
                    prediction_times.append(pred_time)
                    
                    # Convert prediction to display format (1 or 2)
                    display_pred = pred + 1
                    
                    print(f"Time: {timestamp:6.1f}s | Prediction: {display_pred} | "
                          f"Confidence: {prob:.3f} | Processing: {pred_time*1000:.1f}ms")
                    
                    # Check 2-second requirement
                    if pred_time > 2.0:
                        print(f"⚠️  Warning: Prediction took {pred_time:.3f}s (>2s limit)")
                    
                except Exception as e:
                    print(f"Prediction error at {timestamp:.1f}s: {e}")
        
        # Session summary
        print(f"\n{'='*50}")
        print(f"SESSION SUMMARY")
        print(f"{'='*50}")
        print(f"Total predictions: {len(predictions)}")
        
        if prediction_times:
            mean_time = np.mean(prediction_times)
            max_time = np.max(prediction_times)
            print(f"Mean prediction time: {mean_time*1000:.1f}ms")
            print(f"Max prediction time: {max_time*1000:.1f}ms")
            
            if max_time < 2.0:
                print("✅ All predictions within 2-second requirement")
            else:
                print("❌ Some predictions exceeded 2-second requirement")
        
        if predictions:
            pred_counts = np.bincount(predictions)
            print(f"Prediction distribution: {pred_counts}")
        
        return predictions, prediction_times


def test_streaming_bci():
    """Test the streaming BCI system"""
    print("=== Testing Streaming BCI System ===")
    
    # Check if we have a trained model
    model_dir = Path(get_models_path())
    model_files = list(model_dir.glob("*.pkl")) if model_dir.exists() else []
    
    if not model_files:
        print("No trained models found. Training a quick model...")
        
        # Train a quick model for testing
        from pipeline.improved_bci import ImprovedBCIPipeline
        
        bci = ImprovedBCIPipeline(
            n_csp_components=4,
            use_spectral_features=False,
            l_freq=8.0,
            h_freq=30.0
        )
        
        # Train on subject 1
        results = bci.train_and_evaluate(
            subject_ids=[1],
            train_runs=[4, 8],
            cv_folds=3
        )
        
        # Save model
        model_dir.mkdir(exist_ok=True)
        model_path = model_dir / "streaming_test_model.pkl"
        
        with open(model_path, 'wb') as f:
            pickle.dump(bci, f)
        
        print(f"Trained model saved to {model_path}")
    else:
        model_path = model_files[0]
        print(f"Using existing model: {model_path}")
    
    # Initialize streaming BCI
    streaming_bci = StreamingBCI(model_path=str(model_path))
    
    # Run streaming session
    predictions, times = streaming_bci.run_streaming_session(
        subject_id=1,
        run=6,  # Test run
        session_duration=20.0  # 20 second session
    )
    
    return streaming_bci, predictions, times


if __name__ == "__main__":
    streaming_bci, predictions, times = test_streaming_bci()
