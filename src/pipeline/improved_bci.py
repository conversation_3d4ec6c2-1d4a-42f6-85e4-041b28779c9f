"""
Improved BCI Pipeline with better preprocessing and feature extraction
"""

import numpy as np # type: ignore
import mne # type: ignore
from sklearn.pipeline import Pipeline # type: ignore
from sklearn.model_selection import cross_val_score, StratifiedKFold # type: ignore
from sklearn.linear_model import LogisticRegression # type: ignore
from sklearn.preprocessing import StandardScaler # type: ignore
from sklearn.base import BaseEstimator, TransformerMixin # type: ignore
from scipy.signal import welch # type: ignore
import matplotlib.pyplot as plt # type: ignore

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dimensionality.csp import CSP
from config import get_data_path, PREPROCESSING_CONFIG


class ImprovedEEGPreprocessor(BaseEstimator, TransformerMixin):
    """
    Improved EEG preprocessor with better feature extraction
    """
    
    def __init__(self,
                 l_freq=None,
                 h_freq=None,
                 epoch_tmin=None,  # Start 1s after cue to avoid artifacts
                 epoch_tmax=None,
                 baseline=None,  # Use early part of trial as baseline
                 picks=None):  # Motor cortex channels
        """
        Initialize improved preprocessor

        Key improvements:
        - Focus on motor cortex channels
        - Better epoch timing (avoid movement artifacts)
        - Baseline correction
        - Frequency bands optimized for motor imagery
        """
        # Use config defaults if not specified
        self.l_freq = l_freq if l_freq is not None else PREPROCESSING_CONFIG['l_freq']
        self.h_freq = h_freq if h_freq is not None else PREPROCESSING_CONFIG['h_freq']
        self.epoch_tmin = epoch_tmin if epoch_tmin is not None else PREPROCESSING_CONFIG['epoch_tmin']
        self.epoch_tmax = epoch_tmax if epoch_tmax is not None else PREPROCESSING_CONFIG['epoch_tmax']
        self.baseline = baseline if baseline is not None else PREPROCESSING_CONFIG['baseline']
        self.picks = picks if picks is not None else PREPROCESSING_CONFIG['motor_cortex_channels']
        
    def fit(self, X, y=None):
        return self
    
    def transform(self, X):
        """Transform raw EEG data to epoched format with improved preprocessing"""
        all_epochs = []
        all_labels = []
        
        for subject_id, run in X:
            # Load raw data
            raw = self._load_raw_data(subject_id, run)
            
            # Apply improved filtering
            raw_filtered = self._improved_filter(raw)
            
            # Get events
            events, event_id = mne.events_from_annotations(raw_filtered, verbose=False)
            
            # Create epochs with improved parameters
            epochs = self._create_improved_epochs(raw_filtered, events, event_id)
            
            if len(epochs) == 0:
                continue
                
            # Get data and labels
            epoch_data = epochs.get_data()
            labels = epochs.events[:, -1]
            
            all_epochs.append(epoch_data)
            all_labels.append(labels)
        
        if not all_epochs:
            return np.array([]), np.array([])
            
        # Concatenate all data
        X_epochs = np.vstack(all_epochs)
        y_labels = np.hstack(all_labels)
        
        return X_epochs, y_labels
    
    def _load_raw_data(self, subject_id, run, data_dir=None):
        """Load raw EEG data"""
        if data_dir is None:
            data_dir = get_data_path()

        raw_fname = mne.datasets.eegbci.load_data(
            subjects=[subject_id],
            runs=[run],
            path=data_dir,
            update_path=False
        )[0]
        
        raw = mne.io.read_raw_edf(raw_fname, preload=True, verbose=False)
        
        # Set montage
        montage = mne.channels.make_standard_montage('standard_1005')
        try:
            raw.set_montage(montage, verbose=False)
        except ValueError:
            raw.set_montage(montage, on_missing='ignore', verbose=False)
            
        return raw
    
    def _improved_filter(self, raw):
        """Apply improved filtering"""
        raw_filtered = raw.copy()
        
        # Bandpass filter with better parameters for motor imagery
        raw_filtered.filter(
            l_freq=self.l_freq, 
            h_freq=self.h_freq, 
            picks='eeg',
            method='iir',  # IIR filter for better frequency response
            iir_params={'order': 4, 'ftype': 'butter'},
            verbose=False
        )
        
        # Notch filter for power line noise (only 50Hz, 100Hz exceeds Nyquist)
        raw_filtered.notch_filter(
            freqs=50.0,  # Remove 50Hz only (100Hz exceeds Nyquist freq of 80Hz)
            picks='eeg',
            verbose=False
        )
        
        return raw_filtered
    
    def _create_improved_epochs(self, raw, events, event_id):
        """Create epochs with improved parameters"""
        
        # Select only motor imagery events (T1 and T2)
        # T1: left/right fist movement, T2: imagine left/right fist movement
        motor_events = events[np.isin(events[:, 2], [2, 3])]  # T1=2, T2=3
        
        if len(motor_events) == 0:
            return mne.EpochsArray(np.array([]), mne.create_info([], 1), verbose=False)
        
        # Create event_id for motor imagery only
        motor_event_id = {'T1': 2, 'T2': 3}
        
        epochs = mne.Epochs(
            raw,
            motor_events,
            event_id=motor_event_id,
            tmin=self.epoch_tmin,
            tmax=self.epoch_tmax,
            baseline=self.baseline,
            picks=self.picks,  # Use only motor cortex channels
            preload=True,
            reject_by_annotation=True,  # Reject bad segments
            verbose=False
        )
        
        # Drop bad epochs
        epochs.drop_bad()
        
        return epochs


class PowerSpectralFeatures(BaseEstimator, TransformerMixin):
    """
    Extract power spectral features from EEG epochs
    """
    
    def __init__(self, sfreq=160.0, freq_bands=None):
        """
        Initialize power spectral feature extractor
        
        Parameters:
        -----------
        sfreq : float
            Sampling frequency
        freq_bands : dict
            Frequency bands to extract power from
        """
        self.sfreq = sfreq
        
        if freq_bands is None:
            # Default frequency bands for motor imagery
            self.freq_bands = {
                'alpha': (8, 12),
                'beta': (13, 30),
                'mu': (8, 13),  # Mu rhythm (motor cortex)
            }
        else:
            self.freq_bands = freq_bands
    
    def fit(self, X, y=None):
        return self
    
    def transform(self, X):
        """Extract power spectral features"""
        n_epochs, n_channels, n_samples = X.shape
        
        # Calculate number of features
        n_features = len(self.freq_bands) * n_channels
        features = np.zeros((n_epochs, n_features))
        
        for i, epoch in enumerate(X):
            feature_idx = 0
            
            for ch_idx in range(n_channels):
                signal = epoch[ch_idx, :]
                
                # Compute power spectral density
                freqs, psd = welch(signal, fs=self.sfreq, nperseg=min(256, len(signal)))
                
                # Extract power in each frequency band
                for band_name, (low_freq, high_freq) in self.freq_bands.items():
                    # Find frequency indices
                    freq_mask = (freqs >= low_freq) & (freqs <= high_freq)
                    
                    # Calculate mean power in this band
                    band_power = np.mean(psd[freq_mask])
                    
                    # Log transform for better distribution
                    features[i, feature_idx] = np.log(band_power + 1e-10)
                    feature_idx += 1
        
        return features


class ImprovedBCIPipeline:
    """
    Improved BCI pipeline with better preprocessing and features
    """
    
    def __init__(self, 
                 n_csp_components=4,
                 use_spectral_features=True,
                 l_freq=8.0,
                 h_freq=30.0):
        """
        Initialize improved BCI pipeline
        """
        self.n_csp_components = n_csp_components
        self.use_spectral_features = use_spectral_features
        self.l_freq = l_freq
        self.h_freq = h_freq
        
        # Build pipeline
        self.pipeline = self._build_pipeline()
        
    def _build_pipeline(self):
        """Build improved sklearn pipeline"""
        
        steps = [
            ('loader', ImprovedEEGPreprocessor(l_freq=self.l_freq, h_freq=self.h_freq)),
        ]
        
        if self.use_spectral_features:
            # Use spectral features instead of raw CSP
            steps.extend([
                ('spectral', PowerSpectralFeatures()),
                ('scaler', StandardScaler()),
                ('classifier', LogisticRegression(random_state=42, max_iter=1000, C=0.1))
            ])
        else:
            # Use CSP features
            steps.extend([
                ('csp', CSP(n_components=self.n_csp_components, log=True)),
                ('scaler', StandardScaler()),
                ('classifier', LogisticRegression(random_state=42, max_iter=1000, C=0.1))
            ])
        
        return Pipeline(steps)
    
    def prepare_data(self, subject_ids, runs):
        """Prepare data for training/testing"""
        X = []
        for subject_id in subject_ids:
            for run in runs:
                X.append((subject_id, run))
        return X
    
    def train_and_evaluate(self, subject_ids, train_runs, cv_folds=5):
        """Train and evaluate the improved BCI pipeline"""
        print(f"Training improved BCI pipeline for subjects {subject_ids}")
        print(f"Training runs: {train_runs}")
        print(f"Using spectral features: {self.use_spectral_features}")
        
        # Prepare training data
        X_train = self.prepare_data(subject_ids, train_runs)
        
        # Load and transform training data
        loader = ImprovedEEGPreprocessor(l_freq=self.l_freq, h_freq=self.h_freq)
        X_epochs, y_labels = loader.transform(X_train)
        
        if len(X_epochs) == 0:
            print("No data available")
            return {'cv_scores': np.array([]), 'mean_cv_accuracy': 0.0}
        
        print(f"Training data shape: {X_epochs.shape}")
        print(f"Labels shape: {y_labels.shape}")
        print(f"Unique labels: {np.unique(y_labels)}")
        
        # Convert to binary classification (T1 vs T2)
        y_binary = (y_labels == 3).astype(int)  # T2 = 1, T1 = 0
        
        print(f"Class distribution: {np.bincount(y_binary)}")
        
        if len(np.unique(y_binary)) < 2:
            print("Need at least 2 classes for classification")
            return {'cv_scores': np.array([]), 'mean_cv_accuracy': 0.0}
        
        # Cross-validation
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        # Create feature extraction + classifier pipeline
        if self.use_spectral_features:
            feature_pipeline = Pipeline([
                ('spectral', PowerSpectralFeatures()),
                ('scaler', StandardScaler()),
                ('classifier', LogisticRegression(random_state=42, max_iter=1000, C=0.1))
            ])
        else:
            feature_pipeline = Pipeline([
                ('csp', CSP(n_components=self.n_csp_components, log=True)),
                ('scaler', StandardScaler()),
                ('classifier', LogisticRegression(random_state=42, max_iter=1000, C=0.1))
            ])
        
        # Perform cross-validation
        cv_scores = cross_val_score(
            feature_pipeline, 
            X_epochs, 
            y_binary, 
            cv=cv, 
            scoring='accuracy'
        )
        
        print(f"Cross-validation scores: {cv_scores}")
        print(f"Mean CV accuracy: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        # Train final model
        feature_pipeline.fit(X_epochs, y_binary)
        self.trained_pipeline = feature_pipeline
        
        results = {
            'cv_scores': cv_scores,
            'mean_cv_accuracy': cv_scores.mean(),
            'std_cv_accuracy': cv_scores.std(),
            'n_train_samples': len(y_binary),
            'class_distribution': np.bincount(y_binary)
        }
        
        return results


def test_improved_pipeline():
    """Test the improved BCI pipeline"""
    print("=== Testing Improved BCI Pipeline ===")
    
    # Test both approaches
    for use_spectral in [True, False]:
        print(f"\n--- Testing with spectral features: {use_spectral} ---")
        
        bci = ImprovedBCIPipeline(
            n_csp_components=4,
            use_spectral_features=use_spectral,
            l_freq=8.0,
            h_freq=30.0
        )
        
        # Test with subject 1, motor imagery runs
        subject_ids = [1]
        train_runs = [4, 6, 8, 10, 12, 14]  # All motor imagery runs
        
        try:
            results = bci.train_and_evaluate(
                subject_ids=subject_ids,
                train_runs=train_runs,
                cv_folds=5
            )
            
            print(f"Results: {results['mean_cv_accuracy']:.4f} ± {results['std_cv_accuracy']:.4f}")
            
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    test_improved_pipeline()
