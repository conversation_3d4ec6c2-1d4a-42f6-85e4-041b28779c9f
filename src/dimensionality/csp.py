"""
Common Spatial Patterns (CSP) Implementation
Custom sklearn-compatible transformer for EEG motor imagery classification
"""

import numpy as np # type: ignore
from sklearn.base import BaseEstimator, TransformerMixin # type: ignore
from scipy.linalg import eigh # type: ignore
import matplotlib.pyplot as plt # type: ignore


class CSP(BaseEstimator, TransformerMixin):
    """
    Common Spatial Patterns (CSP) for EEG motor imagery classification
    
    CSP finds spatial filters that maximize the variance of one class
    while minimizing the variance of another class. This is particularly
    effective for motor imagery tasks where different brain regions
    are activated for different movements.
    
    The algorithm:
    1. Compute covariance matrices for each class
    2. Solve generalized eigenvalue problem
    3. Select filters that maximize class separability
    4. Apply spatial filtering to transform data
    """
    
    def __init__(self, n_components=4, reg=None, log=True, norm_trace=False):
        """
        Initialize CSP transformer
        
        Parameters:
        -----------
        n_components : int
            Number of CSP components to keep (should be even)
            We typically take n_components/2 from each end of eigenvalue spectrum
        reg : float or None
            Regularization parameter for covariance matrix estimation
        log : bool
            Whether to apply log transformation to features
        norm_trace : bool
            Whether to normalize covariance matrices by their trace
        """
        self.n_components = n_components
        self.reg = reg
        self.log = log
        self.norm_trace = norm_trace
        
        # Will be set during fit
        self.filters_ = None
        self.patterns_ = None
        self.eigenvalues_ = None
        
    def _compute_covariance(self, X):
        """
        Compute covariance matrix for data X
        
        Parameters:
        -----------
        X : array, shape (n_trials, n_channels, n_samples)
            EEG data
            
        Returns:
        --------
        cov : array, shape (n_channels, n_channels)
            Covariance matrix
        """
        n_trials, n_channels, n_samples = X.shape
        
        # Initialize covariance matrix
        cov = np.zeros((n_channels, n_channels))
        
        # Compute covariance for each trial and average
        for trial in X:
            # trial shape: (n_channels, n_samples)
            trial_cov = np.cov(trial)
            
            # Normalize by trace if requested
            if self.norm_trace:
                trial_cov = trial_cov / np.trace(trial_cov)
                
            cov += trial_cov
            
        # Average across trials
        cov /= n_trials
        
        # Add regularization if specified
        if self.reg is not None:
            cov += self.reg * np.eye(n_channels)
            
        return cov
    
    def fit(self, X, y):
        """
        Fit CSP spatial filters
        
        Parameters:
        -----------
        X : array, shape (n_trials, n_channels, n_samples)
            EEG data
        y : array, shape (n_trials,)
            Class labels (should be binary: 0 and 1, or -1 and 1)
            
        Returns:
        --------
        self : CSP instance
        """
        # Convert to numpy arrays
        X = np.asarray(X)
        y = np.asarray(y)
        
        # Get unique classes
        classes = np.unique(y)
        if len(classes) != 2:
            raise ValueError(f"CSP requires exactly 2 classes, got {len(classes)}")
        
        # Split data by class
        X_class1 = X[y == classes[0]]
        X_class2 = X[y == classes[1]]
        
        print(f"Class 1: {len(X_class1)} trials, Class 2: {len(X_class2)} trials")
        
        # Compute covariance matrices for each class
        cov1 = self._compute_covariance(X_class1)
        cov2 = self._compute_covariance(X_class2)
        
        print(f"Covariance matrix shape: {cov1.shape}")
        
        # Solve generalized eigenvalue problem
        # We want to find W such that:
        # W^T * cov1 * W = D (diagonal)
        # W^T * cov2 * W = I (identity)
        # This is equivalent to solving: cov1 * W = (cov1 + cov2) * W * D
        
        eigenvalues, eigenvectors = eigh(cov1, cov1 + cov2)
        
        # Sort by eigenvalues (descending order)
        idx = np.argsort(eigenvalues)[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]
        
        # Select components
        # Take n_components/2 from each end of the spectrum
        n_comp_half = self.n_components // 2
        
        # Indices for selected components
        selected_idx = np.concatenate([
            np.arange(n_comp_half),  # First n_comp_half (highest eigenvalues)
            np.arange(-n_comp_half, 0)  # Last n_comp_half (lowest eigenvalues)
        ])
        
        self.filters_ = eigenvectors[:, selected_idx].T  # Shape: (n_components, n_channels)
        self.eigenvalues_ = eigenvalues[selected_idx]
        
        # Compute patterns (inverse of filters)
        # Patterns show the spatial distribution of the components
        self.patterns_ = np.linalg.pinv(self.filters_).T
        
        print(f"Selected {self.n_components} CSP components")
        print(f"Eigenvalues: {self.eigenvalues_}")
        
        return self
    
    def transform(self, X):
        """
        Apply CSP spatial filtering to data
        
        Parameters:
        -----------
        X : array, shape (n_trials, n_channels, n_samples)
            EEG data to transform
            
        Returns:
        --------
        X_csp : array, shape (n_trials, n_components)
            CSP features (log variance of filtered signals)
        """
        if self.filters_ is None:
            raise ValueError("CSP must be fitted before transform")
            
        X = np.asarray(X)
        n_trials, n_channels, n_samples = X.shape
        
        # Apply spatial filters
        X_filtered = np.zeros((n_trials, self.n_components, n_samples))
        
        for i, trial in enumerate(X):
            # Apply filters: filtered_trial = filters @ trial
            X_filtered[i] = self.filters_ @ trial
        
        # Compute features: log variance of each filtered signal
        features = np.zeros((n_trials, self.n_components))
        
        for i in range(n_trials):
            for j in range(self.n_components):
                # Compute variance of filtered signal
                var = np.var(X_filtered[i, j, :])
                
                # Apply log transformation if requested
                if self.log:
                    features[i, j] = np.log(var)
                else:
                    features[i, j] = var
        
        return features
    
    def fit_transform(self, X, y):
        """Fit CSP and transform data in one step"""
        return self.fit(X, y).transform(X)
    
    def plot_patterns(self, info=None, show=True):
        """
        Plot CSP spatial patterns
        
        Parameters:
        -----------
        info : mne.Info or None
            MNE info object for channel positions
        show : bool
            Whether to display the plot
        """
        if self.patterns_ is None:
            raise ValueError("CSP must be fitted before plotting patterns")
        
        n_components = self.patterns_.shape[1]
        n_cols = min(4, n_components)
        n_rows = (n_components + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(3*n_cols, 3*n_rows))
        if n_components == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i in range(min(n_components, self.n_components)):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]

            # Simple visualization of pattern weights
            pattern = self.patterns_[:, i]

            # Create a simple bar plot of channel weights
            channels = range(len(pattern))
            ax.bar(channels, pattern)
            ax.set_title(f'CSP Component {i+1}\n(λ={self.eigenvalues_[i]:.3f})')
            ax.set_xlabel('Channel Index')
            ax.set_ylabel('Weight')
            ax.grid(True, alpha=0.3)
        
        # Hide unused subplots
        for i in range(n_components, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            ax.set_visible(False)
        
        plt.tight_layout()
        
        if show:
            plt.show()
            
        return fig
    
    def get_feature_names_out(self, input_features=None):
        """Get output feature names for sklearn compatibility"""
        if self.filters_ is None:
            raise ValueError("CSP must be fitted before getting feature names")
        
        return [f'csp_component_{i}' for i in range(self.n_components)]


def test_csp():
    """Test CSP implementation with synthetic data"""
    print("=== Testing CSP Implementation ===")
    
    # Create synthetic EEG data
    n_trials = 100
    n_channels = 8
    n_samples = 500
    
    # Generate synthetic data with different spatial patterns for each class
    np.random.seed(42)
    
    # Class 1: activity in first half of channels
    X_class1 = np.random.randn(n_trials//2, n_channels, n_samples)
    X_class1[:, :n_channels//2, :] *= 2  # Increase variance in first half
    
    # Class 2: activity in second half of channels  
    X_class2 = np.random.randn(n_trials//2, n_channels, n_samples)
    X_class2[:, n_channels//2:, :] *= 2  # Increase variance in second half
    
    # Combine data
    X = np.vstack([X_class1, X_class2])
    y = np.hstack([np.zeros(n_trials//2), np.ones(n_trials//2)])
    
    print(f"Synthetic data shape: {X.shape}")
    print(f"Labels shape: {y.shape}")
    
    # Fit CSP
    csp = CSP(n_components=4, log=True)
    X_csp = csp.fit_transform(X, y)
    
    print(f"CSP features shape: {X_csp.shape}")
    print(f"Feature names: {csp.get_feature_names_out()}")
    
    # Plot patterns
    csp.plot_patterns(show=False)
    
    # Show class separability
    plt.figure(figsize=(10, 6))
    
    # Plot first two CSP components
    class1_mask = y == 0
    class2_mask = y == 1
    
    plt.subplot(1, 2, 1)
    plt.scatter(X_csp[class1_mask, 0], X_csp[class1_mask, 1], 
                alpha=0.6, label='Class 1', c='blue')
    plt.scatter(X_csp[class2_mask, 0], X_csp[class2_mask, 1], 
                alpha=0.6, label='Class 2', c='red')
    plt.xlabel('CSP Component 1')
    plt.ylabel('CSP Component 2')
    plt.title('CSP Feature Space')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot feature distributions
    plt.subplot(1, 2, 2)
    plt.hist(X_csp[class1_mask, 0], alpha=0.6, label='Class 1', bins=20)
    plt.hist(X_csp[class2_mask, 0], alpha=0.6, label='Class 2', bins=20)
    plt.xlabel('CSP Component 1')
    plt.ylabel('Count')
    plt.title('Feature Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return csp, X_csp, y


if __name__ == "__main__":
    csp, X_csp, y = test_csp()
