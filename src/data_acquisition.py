"""
Data acquisition and exploration for EEG motor imagery data from PhysioNet
"""

import os
import mne # type: ignore
import numpy as np # type: ignore
import matplotlib.pyplot as plt # type: ignore
from pathlib import Path
from config import get_data_path, EEG_CONFIG


def download_physionet_data(subject_ids=None, data_dir=None):
    """
    Download EEG Motor Movement/Imagery Dataset from PhysioNet
    
    This dataset contains EEG recordings from 109 subjects performing motor imagery tasks.
    Each subject performed 14 experimental runs:
    - 2 baseline runs (eyes open/closed)
    - 3 runs of each of 4 motor imagery tasks:
      - Task 1: Open and close left or right fist
      - Task 2: Imagine opening and closing left or right fist  
      - Task 3: Open and close both fists or both feet
      - Task 4: Imagine opening and closing both fists or both feet
    """
    
    if subject_ids is None:
        subject_ids = [1, 2, 3, 4]  # Start with first 4 subjects for testing

    if data_dir is None:
        data_dir = get_data_path()

    data_path = Path(data_dir)
    data_path.mkdir(exist_ok=True)
    
    downloaded_files = []
    
    for subject_id in subject_ids:
        print(f"Downloading data for subject {subject_id:03d}...")
        
        # Download runs for this subject
        for run in range(1, 15):  # 14 runs per subject
            try:
                # Use MNE's built-in PhysioNet downloader
                raw_fname = mne.datasets.eegbci.load_data(
                    subjects=[subject_id],
                    runs=[run],
                    path=str(data_path),
                    update_path=False
                )[0]
                downloaded_files.append(raw_fname)
                print(f"  Downloaded run {run:02d}")
                
            except Exception as e:
                print(f"  Error downloading run {run:02d}: {e}")
                continue
    
    print(f"Downloaded {len(downloaded_files)} files to {data_path}")
    return downloaded_files


def load_and_explore_data(subject_id=1, run=4, data_dir=None):
    """
    Load and explore a single EEG file
    """
    print(f"Loading subject {subject_id:03d}, run {run:02d}")

    if data_dir is None:
        data_dir = get_data_path()

    # Load the data
    raw_fname = mne.datasets.eegbci.load_data(
        subjects=[subject_id],
        runs=[run],
        path=data_dir,
        update_path=False
    )[0]
    
    # Read the raw data
    raw = mne.io.read_raw_edf(raw_fname, preload=True, verbose=False)

    # Set montage (electrode positions) - handle channel naming
    montage = mne.channels.make_standard_montage('standard_1005')
    try:
        raw.set_montage(montage, verbose=False)
    except ValueError:
        # If montage doesn't match, set it with on_missing='ignore'
        raw.set_montage(montage, on_missing='ignore', verbose=False)
    
    print(f"Data shape: {raw.get_data().shape}")
    print(f"Sampling frequency: {raw.info['sfreq']} Hz")
    print(f"Duration: {raw.times[-1]:.1f} seconds")
    print(f"Channels: {raw.info['ch_names']}")
    
    return raw


def explore_events_and_annotations(raw):
    """
    Explore events and annotations in the EEG data
    """
    # Find events
    events, event_id = mne.events_from_annotations(raw, verbose=False)
    
    print(f"\nFound {len(events)} events")
    print(f"Event types: {event_id}")
    
    # Print first few events
    print("\nFirst 10 events:")
    print("Sample\tTime(s)\tEvent")
    for i in range(min(10, len(events))):
        sample, _, event_code = events[i]
        time = sample / raw.info['sfreq']
        print(f"{sample}\t{time:.2f}\t{event_code}")
    
    return events, event_id


def visualize_raw_data(raw, duration=10, start=60):
    """
    Visualize raw EEG data
    """
    print(f"\nPlotting {duration}s of raw data starting at {start}s...")
    
    # Plot raw data
    fig = raw.plot(
        start=start, 
        duration=duration, 
        n_channels=10,  # Show first 10 channels
        scalings='auto',
        title=f"Raw EEG Data ({start}-{start+duration}s)",
        show=False
    )
    
    return fig


def analyze_frequency_content(raw, channel='C3..'):
    """
    Analyze frequency content of a specific channel
    """
    print(f"\nAnalyzing frequency content of channel {channel}...")
    
    # Get data for specific channel
    ch_idx = raw.ch_names.index(channel)
    data = raw.get_data()[ch_idx, :]
    
    # Compute power spectral density
    freqs, psd = mne.time_frequency.psd_array_welch(
        data[np.newaxis, :], 
        sfreq=raw.info['sfreq'],
        fmin=1, fmax=50,
        verbose=False
    )
    
    # Plot PSD
    plt.figure(figsize=(10, 6))
    plt.semilogy(freqs, psd[0], 'b-', linewidth=1.5)
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Power Spectral Density (V²/Hz)')
    plt.title(f'Power Spectral Density - Channel {channel}')
    plt.grid(True, alpha=0.3)
    
    # Highlight important frequency bands
    plt.axvspan(8, 12, alpha=0.2, color='green', label='Alpha (8-12 Hz)')
    plt.axvspan(13, 30, alpha=0.2, color='blue', label='Beta (13-30 Hz)')
    plt.axvspan(4, 8, alpha=0.2, color='orange', label='Theta (4-8 Hz)')
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    return freqs, psd


def main():
    """
    Main function to demonstrate data acquisition and exploration
    """
    print("=== EEG Data Acquisition and Exploration ===\n")
    
    # Download sample data
    print("1. Downloading sample data...")
    downloaded_files = download_physionet_data(subject_ids=[1, 2])
    
    # Load and explore data
    print("\n2. Loading and exploring data...")
    raw = load_and_explore_data(subject_id=1, run=4)  # Motor imagery task
    
    # Explore events
    print("\n3. Exploring events and annotations...")
    events, event_id = explore_events_and_annotations(raw)
    
    # Visualize raw data
    print("\n4. Visualizing raw data...")
    fig = visualize_raw_data(raw)
    
    # Analyze frequency content
    print("\n5. Analyzing frequency content...")
    freqs, psd = analyze_frequency_content(raw, channel='C3..')
    
    print("\n=== Data exploration complete! ===")
    
    return raw, events, event_id


if __name__ == "__main__":
    raw, events, event_id = main()
