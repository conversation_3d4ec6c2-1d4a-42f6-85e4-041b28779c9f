"""
Configuration file for Total Perspective Vortex BCI System
Centralizes all data paths and configuration settings
"""

from pathlib import Path

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent

# Data directory - all EEG data goes here
DATA_DIR = PROJECT_ROOT / "data"

# Models directory - all trained models go here  
MODELS_DIR = PROJECT_ROOT / "models"

# Ensure directories exist
DATA_DIR.mkdir(exist_ok=True)
MODELS_DIR.mkdir(exist_ok=True)

# EEG Data Configuration
EEG_CONFIG = {
    'data_dir': str(DATA_DIR),
    'sampling_freq': 160.0,
    'motor_imagery_runs': [4, 6, 8, 10, 12, 14],
    'baseline_runs': [1, 2],
    'all_runs': list(range(1, 15))
}

# Preprocessing Configuration
PREPROCESSING_CONFIG = {
    'l_freq': 8.0,
    'h_freq': 30.0,
    'notch_freq': 50.0,
    'epoch_tmin': 1.0,
    'epoch_tmax': 4.0,
    'baseline': (1.0, 2.0),
    'motor_cortex_channels': ['C3..', 'Cz..', 'C4..', 'Cp3.', 'Cpz.', 'Cp4.']
}

# CSP Configuration
CSP_CONFIG = {
    'n_components': 4,
    'log_transform': True,
    'regularization': None
}

# Classification Configuration
CLASSIFICATION_CONFIG = {
    'cv_folds': 5,
    'test_size': 0.2,
    'random_state': 42,
    'accuracy_threshold': 0.6
}

# Model Configuration
MODEL_CONFIG = {
    'models_dir': str(MODELS_DIR),
    'model_filename_template': 'bci_model_s{subject:03d}_e{experiment:02d}.pkl'
}

# Streaming Configuration
STREAMING_CONFIG = {
    'buffer_size': 4.0,  # seconds
    'prediction_window': 4.0,  # seconds
    'chunk_duration': 0.5,  # seconds
    'max_prediction_time': 2.0  # seconds
}


def get_data_path():
    """Get the centralized data directory path"""
    return str(DATA_DIR)


def get_models_path():
    """Get the centralized models directory path"""
    return str(MODELS_DIR)


def get_model_path(subject_id, experiment_id):
    """Get path for a specific model file"""
    filename = MODEL_CONFIG['model_filename_template'].format(
        subject=subject_id, 
        experiment=experiment_id
    )
    return MODELS_DIR / filename


def print_config():
    """Print current configuration"""
    print("=== Total Perspective Vortex Configuration ===")
    print(f"Project Root: {PROJECT_ROOT}")
    print(f"Data Directory: {DATA_DIR}")
    print(f"Models Directory: {MODELS_DIR}")
    print(f"EEG Config: {EEG_CONFIG}")
    print(f"Preprocessing Config: {PREPROCESSING_CONFIG}")
    print(f"CSP Config: {CSP_CONFIG}")
    print(f"Classification Config: {CLASSIFICATION_CONFIG}")
    print(f"Streaming Config: {STREAMING_CONFIG}")


if __name__ == "__main__":
    print_config()
