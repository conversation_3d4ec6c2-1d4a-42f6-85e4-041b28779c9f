# How to Run the Total Perspective Vortex BCI System

## 🧠 What is This Project?

The **Total Perspective Vortex** is a **Brain-Computer Interface (BCI)** system that can read your thoughts! 

Specifically, it analyzes brain signals (EEG) to determine what type of movement you're thinking about - like imagining moving your left hand vs right hand. This is called **motor imagery** classification.

**Real-world applications:**
- Help paralyzed patients control prosthetic limbs with their thoughts
- Control computers/wheelchairs with brain signals
- Neurofeedback therapy
- Brain-controlled gaming

## 🚀 Quick Start (5 Minutes)

### Step 1: Get the Code
```bash
# Clone or download the project
cd total-perspective-vortex/
```

### Step 2: Set Up Environment
```bash
# Create virtual environment (recommended)
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install required packages
pip install -r requirements.txt
```

### Step 3: Test the System
```bash
# Train a brain-computer interface model
python mybci.py 1 4 train

# Make predictions with the trained model
python mybci.py 1 4 predict

# Run full evaluation
python mybci.py
```

**That's it! You now have a working brain-computer interface!** 🎉

## 📖 Detailed Instructions

### Prerequisites

**What you need:**
- Python 3.8+ installed
- About 2GB free disk space (for EEG data)
- Internet connection (to download brain data)
- 10-15 minutes for first run

**No prior knowledge needed of:**
- Neuroscience
- Machine learning
- Signal processing

### Installation Steps

#### 1. **Download the Project**
```bash
# If you have git:
git clone <repository-url>
cd total-perspective-vortex/

# Or download and extract the ZIP file
```

#### 2. **Set Up Python Environment**
```bash
# Check Python version (need 3.8+)
python3 --version

# Create isolated environment (recommended)
python3 -m venv venv

# Activate environment
source venv/bin/activate        # Linux/Mac
# OR
venv\Scripts\activate          # Windows
```

#### 3. **Install Dependencies**
```bash
# Install all required packages
pip install -r requirements.txt

# This installs:
# - MNE: Brain signal processing
# - scikit-learn: Machine learning
# - NumPy/SciPy: Math libraries
# - Matplotlib: Plotting
```

#### 4. **Verify Installation**
```bash
# Test that everything is set up correctly
python test_data_paths.py

# Should show: "🎉 ALL PATHS CORRECTLY CONFIGURED! 🎉"
```

## 🎮 How to Use the System

### Basic Commands

The main interface is `mybci.py` with three modes:

#### **1. Train a Model**
```bash
python mybci.py <subject> <experiment> train
```

**Example:**
```bash
python mybci.py 1 4 train
```
- Downloads brain data for Subject 1, Experiment 4
- Trains AI to recognize thought patterns
- Shows accuracy score (aim for >60%)

#### **2. Make Predictions**
```bash
python mybci.py <subject> <experiment> predict
```

**Example:**
```bash
python mybci.py 1 4 predict
```
- Uses trained model to predict thoughts
- Shows prediction vs reality for each brain signal
- Displays overall accuracy

#### **3. Full Evaluation**
```bash
python mybci.py
```
- Tests system on multiple subjects
- Comprehensive performance evaluation
- Shows if system meets 60% accuracy requirement

### Understanding the Output

#### **Training Output:**
```bash
$ python mybci.py 1 4 train
Training model for subject 1, experiment 4
Training data shape: (90, 64, 641)    # 90 brain recordings, 64 electrodes, 641 time points
Binary classification data: (45, 64, 641)  # 45 recordings for each thought type
Cross-validation scores: [1.0, 0.8, 0.6, ...]  # Accuracy on different data splits
cross_val_score: 0.7550               # 75.5% accuracy - GOOD! (>60% required)
```

#### **Prediction Output:**
```bash
$ python mybci.py 1 4 predict
epoch nb: [prediction] [truth] equal?
epoch 00: [2] [1] False    # Predicted thought type 2, actual was 1 - wrong
epoch 01: [1] [1] True     # Predicted thought type 1, actual was 1 - correct!
epoch 02: [2] [1] False    # Wrong again
...
Accuracy: 0.6667           # 66.7% correct predictions
```

**What the numbers mean:**
- **[1]** = Thinking about one type of movement (e.g., left hand)
- **[2]** = Thinking about another type of movement (e.g., right hand)
- **True/False** = Whether the AI guessed correctly

## 🧪 Advanced Usage

### Explore the Data
```bash
# Look at raw brain signals
cd src/
python data_acquisition.py

# Test different algorithms
cd pipeline/
python improved_bci.py

# Try real-time brain reading
python streaming_bci.py
```

### Test Different Subjects
```bash
# Try different people's brain data
python mybci.py 2 6 train    # Subject 2, Experiment 6
python mybci.py 3 8 train    # Subject 3, Experiment 8
python mybci.py 4 10 train   # Subject 4, Experiment 10
```

### Run Comprehensive Tests
```bash
# Full system evaluation
python final_evaluation.py

# Test data organization
python test_data_paths.py
```

## 📊 Understanding the Science

### What Happens When You Run the System:

1. **Data Download**: Gets real brain recordings from 109 people
2. **Preprocessing**: Cleans up the brain signals (removes noise)
3. **Feature Extraction**: Finds patterns that distinguish different thoughts
4. **Machine Learning**: Trains AI to recognize these patterns
5. **Classification**: Predicts what someone was thinking based on their brain signals

### The Brain Data:

- **Source**: PhysioNet EEG Motor Movement/Imagery Dataset
- **Subjects**: 109 people
- **Tasks**: Imagining hand/foot movements
- **Electrodes**: 64 sensors on the scalp
- **Sampling**: 160 measurements per second

### The AI Algorithm:

- **CSP (Common Spatial Patterns)**: Finds brain regions that differ between thoughts
- **Logistic Regression**: Makes final predictions
- **Cross-validation**: Tests accuracy on unseen data

## 🔧 Troubleshooting

### Common Issues:

#### **"ModuleNotFoundError"**
```bash
# Make sure virtual environment is activated
source venv/bin/activate

# Reinstall packages
pip install -r requirements.txt
```

#### **"Permission denied" or "Cannot create directory"**
```bash
# Make sure you have write permissions
chmod 755 .
mkdir -p data models
```

#### **Low accuracy (<50%)**
- This is normal for some subjects - brain signals vary between people
- Try different subjects: `python mybci.py 2 6 train`
- The system averages >60% across all subjects

#### **Slow performance**
- First run downloads ~1GB of brain data (takes time)
- Subsequent runs are much faster
- Training takes 1-2 minutes per subject

### Getting Help:

1. **Check the logs**: Look for error messages in the terminal
2. **Verify setup**: Run `python test_data_paths.py`
3. **Try simple test**: `python mybci.py 1 4 train`

## 📁 Project Structure

```
total-perspective-vortex/
├── 📄 mybci.py              # Main interface - START HERE
├── 📄 HOW_TO_RUN.md         # This guide
├── 📄 requirements.txt      # Required packages
├── 📁 data/                 # Brain data (auto-downloaded)
├── 📁 models/               # Trained AI models (auto-created)
└── 📁 src/                  # Source code
    ├── 📄 config.py         # Configuration
    ├── 📁 preprocessing/    # Signal processing
    ├── 📁 dimensionality/   # CSP algorithm
    └── 📁 pipeline/         # Complete system
```

## 🎯 Success Criteria

**Your system is working correctly if:**

✅ **Training shows >60% accuracy**
```bash
cross_val_score: 0.7550  # 75.5% - EXCELLENT!
```

✅ **Predictions run without errors**
```bash
Accuracy: 0.6667  # 66.7% - GOOD!
```

✅ **Full evaluation passes**
```bash
Mean accuracy of 6 experiments: 0.6261  # 62.6% - SUCCESS!
```

## 🚀 Next Steps

Once you have the basic system running:

1. **Experiment with different subjects** - see how brain patterns vary
2. **Try the streaming mode** - real-time brain reading
3. **Explore the visualizations** - see what brain signals look like
4. **Modify parameters** - tune the AI for better performance
5. **Read the code** - understand how brain-computer interfaces work

## 🎉 Congratulations!

You now have a working brain-computer interface that can read thoughts from brain signals! This is the same technology used in:

- Medical devices for paralyzed patients
- Research laboratories worldwide
- Next-generation human-computer interfaces

**Welcome to the future of brain-computer interaction!** 🧠✨

---

*"Have some sense of proportion!" - Douglas Adams*

*The Total Perspective Vortex shows us the whole infinity of brain signals and our place in understanding them.*
