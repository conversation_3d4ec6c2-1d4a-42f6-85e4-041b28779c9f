# Total Perspective Vortex
## Brain Computer Interface with Machine Learning

This project implements a brain-computer interface (BCI) based on electroencephalographic (EEG) data using machine learning algorithms to classify motor imagery tasks.

## Project Structure

```
total-perspective-vortex/
├── data/                    # 📁 ALL EEG data from PhysioNet (centralized)
├── models/                  # 📁 ALL trained BCI models (centralized)
├── src/                     # Source code
│   ├── config.py           # 🔧 Centralized configuration (data paths, settings)
│   ├── preprocessing/       # Data preprocessing and filtering
│   ├── dimensionality/     # Dimensionality reduction (CSP)
│   ├── pipeline/           # Complete processing pipeline
│   └── data_acquisition.py # PhysioNet data loading
├── mybci.py               # Main CLI interface
├── final_evaluation.py    # Comprehensive evaluation
├── test_data_paths.py     # Data path verification
└── requirements.txt       # Dependencies

```

## 📁 Data Organization

**All data is centrally managed:**
- **EEG Data**: `./data/` - All PhysioNet EEG files
- **Models**: `./models/` - All trained BCI models
- **Configuration**: `src/config.py` - Centralized paths and settings

No data is scattered throughout the codebase - everything uses the centralized configuration.

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Download EEG data (will be automated in data acquisition script)

## Usage

### Training
```bash
python mybci.py 4 14 train
```

### Prediction
```bash
python mybci.py 4 14 predict
```

### Full evaluation
```bash
python mybci.py
```

## Goals

- [x] Process EEG data (parsing and filtering)
- [ ] Implement dimensionality reduction algorithm (CSP)
- [ ] Use sklearn pipeline object
- [ ] Classify data stream in "real time" (<2s delay)
- [ ] Achieve 60% mean accuracy across all subjects

## Key Components

1. **Preprocessing**: EEG signal filtering and feature extraction
2. **CSP Algorithm**: Custom implementation as sklearn transformer
3. **Classification**: Motor imagery task classification
4. **Real-time Processing**: Streaming data simulation
