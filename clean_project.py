#!/usr/bin/env python3
"""
Total Perspective Vortex - Project Cleaner
Safely removes data and models to reset the project to a clean state
"""

import sys
import shutil
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from config import get_data_path, get_models_path, DATA_DIR, MODELS_DIR # type: ignore


def get_directory_size(path):
    """Calculate total size of directory in MB"""
    total_size = 0
    try:
        for file_path in Path(path).rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
    except (OSError, PermissionError):
        return 0
    return total_size / (1024 * 1024)  # Convert to MB


def list_directory_contents(path, max_items=10):
    """List contents of directory with size info"""
    path = Path(path)
    if not path.exists():
        return []
    
    items = []
    try:
        for item in path.iterdir():
            if item.is_file():
                size_mb = item.stat().st_size / (1024 * 1024)
                items.append(f"  📄 {item.name} ({size_mb:.1f} MB)")
            elif item.is_dir():
                dir_size = get_directory_size(item)
                items.append(f"  📁 {item.name}/ ({dir_size:.1f} MB)")
    except (OSError, PermissionError):
        items.append("  ❌ Permission denied")
    
    if len(items) > max_items:
        items = items[:max_items] + [f"  ... and {len(items) - max_items} more items"]
    
    return items


def show_current_state():
    """Display current project state"""
    print("=" * 60)
    print("TOTAL PERSPECTIVE VORTEX - CURRENT PROJECT STATE")
    print("=" * 60)
    
    # Data directory
    data_path = Path(get_data_path())
    data_size = get_directory_size(data_path) if data_path.exists() else 0
    
    print(f"\n📁 DATA DIRECTORY: {data_path}")
    print(f"   Size: {data_size:.1f} MB")
    print(f"   Exists: {data_path.exists()}")
    
    if data_path.exists():
        contents = list_directory_contents(data_path)
        if contents:
            print("   Contents:")
            for item in contents:
                print(item)
        else:
            print("   Contents: Empty")
    
    # Models directory
    models_path = Path(get_models_path())
    models_size = get_directory_size(models_path) if models_path.exists() else 0
    
    print(f"\n🤖 MODELS DIRECTORY: {models_path}")
    print(f"   Size: {models_size:.1f} MB")
    print(f"   Exists: {models_path.exists()}")
    
    if models_path.exists():
        contents = list_directory_contents(models_path)
        if contents:
            print("   Contents:")
            for item in contents:
                print(item)
        else:
            print("   Contents: Empty")
    
    # Total size
    total_size = data_size + models_size
    print(f"\n💾 TOTAL SIZE: {total_size:.1f} MB")
    
    return data_path, models_path, total_size


def clean_data(force=False):
    """Clean data directory contents but keep the directory structure"""
    data_path = Path(get_data_path())
    
    if not data_path.exists():
        print("📁 Data directory doesn't exist - creating empty directory")
        data_path.mkdir(parents=True, exist_ok=True)
        print("✅ Empty data directory created")
        return True
    
    data_size = get_directory_size(data_path)
    
    if not force:
        print(f"\n⚠️  About to remove DATA directory:")
        print(f"   Path: {data_path}")
        print(f"   Size: {data_size:.1f} MB")
        print(f"   Contents:")
        
        contents = list_directory_contents(data_path)
        for item in contents:
            print(item)
        
        response = input(f"\n❓ Clean data directory? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Data cleaning cancelled")
            return False
    
    try:
        print(f"🗑️  Removing data directory contents...")
        shutil.rmtree(data_path)
        print(f"📁 Recreating empty data directory...")
        data_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Data directory cleaned ({data_size:.1f} MB freed)")
        return True
    except Exception as e:
        print(f"❌ Error cleaning data directory: {e}")
        return False


def clean_models(force=False):
    """Clean models directory contents but keep the directory structure"""
    models_path = Path(get_models_path())
    
    if not models_path.exists():
        print("📁 Models directory doesn't exist - creating empty directory")
        models_path.mkdir(parents=True, exist_ok=True)
        print("✅ Empty models directory created")
        return True
    
    models_size = get_directory_size(models_path)
    
    if not force:
        print(f"\n⚠️  About to remove MODELS directory:")
        print(f"   Path: {models_path}")
        print(f"   Size: {models_size:.1f} MB")
        print(f"   Contents:")
        
        contents = list_directory_contents(models_path)
        for item in contents:
            print(item)
        
        response = input(f"\n❓ Clean models directory? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Models cleaning cancelled")
            return False
    
    try:
        print(f"🗑️  Removing models directory contents...")
        shutil.rmtree(models_path)
        print(f"📁 Recreating empty models directory...")
        models_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Models directory cleaned ({models_size:.1f} MB freed)")
        return True
    except Exception as e:
        print(f"❌ Error cleaning models directory: {e}")
        return False


def clean_all(force=False):
    """Clean both data and models directories but keep directory structure"""
    data_path, models_path, total_size = show_current_state()
    
    if not data_path.exists() and not models_path.exists():
        print("\n✅ Project is already clean - nothing to remove")
        return True
    
    if not force:
        print(f"\n⚠️  COMPLETE PROJECT CLEANUP")
        print(f"   This will clean ALL data and models ({total_size:.1f} MB)")
        print(f"   Empty data/ and models/ directories will remain")
        print(f"   You will need to re-download data and retrain models")
        
        response = input(f"\n❓ Proceed with complete cleanup? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Complete cleanup cancelled")
            return False
    
    print(f"\n🧹 Starting complete project cleanup...")
    
    success = True
    
    # Clean data
    if data_path.exists():
        success &= clean_data(force=True)
    
    # Clean models  
    if models_path.exists():
        success &= clean_models(force=True)
    
    if success:
        print(f"\n🎉 Project cleanup complete!")
        print(f"   Freed {total_size:.1f} MB of disk space")
        print(f"   Project reset to clean state")
        print(f"   📁 Empty data/ and models/ directories ready for use")
    else:
        print(f"\n⚠️  Cleanup completed with some errors")
    
    return success


def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description="Clean Total Perspective Vortex project data and models",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python clean_project.py --status          # Show current state
  python clean_project.py --data            # Clean data (keep empty folder)
  python clean_project.py --models          # Clean models (keep empty folder)
  python clean_project.py --all             # Clean everything (keep empty folders)
  python clean_project.py --all --force     # Clean everything without prompts
        """
    )
    
    parser.add_argument("--status", action="store_true", 
                       help="Show current project state")
    parser.add_argument("--data", action="store_true",
                       help="Clean data directory (keeps empty folder)")
    parser.add_argument("--models", action="store_true",
                       help="Clean models directory (keeps empty folder)")
    parser.add_argument("--all", action="store_true",
                       help="Clean both data and models (keeps empty folders)")
    parser.add_argument("--force", action="store_true",
                       help="Skip confirmation prompts")
    
    args = parser.parse_args()
    
    # If no arguments, show status by default
    if not any([args.status, args.data, args.models, args.all]):
        args.status = True
    
    try:
        if args.status:
            show_current_state()
            
        elif args.all:
            clean_all(force=args.force)
            
        elif args.data:
            clean_data(force=args.force)
            
        elif args.models:
            clean_models(force=args.force)
            
    except KeyboardInterrupt:
        print(f"\n\n❌ Cleanup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
