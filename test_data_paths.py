#!/usr/bin/env python3
"""
Test script to verify all data paths are correctly configured
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from config import get_data_path, get_models_path, get_model_path, print_config # type: ignore


def test_data_paths():
    """Test that all data paths are correctly configured"""
    print("=== Testing Data Path Configuration ===\n")
    
    # Print current configuration
    print_config()
    
    print("\n=== Path Tests ===")
    
    # Test data path
    data_path = get_data_path()
    print(f"Data path: {data_path}")
    print(f"Data path exists: {Path(data_path).exists()}")
    print(f"Data path is absolute: {Path(data_path).is_absolute()}")
    
    # Test models path
    models_path = get_models_path()
    print(f"Models path: {models_path}")
    print(f"Models path exists: {Path(models_path).exists()}")
    print(f"Models path is absolute: {Path(models_path).is_absolute()}")
    
    # Test model file path
    model_path = get_model_path(1, 4)
    print(f"Sample model path: {model_path}")
    print(f"Model path parent exists: {model_path.parent.exists()}")
    
    # Check if paths are in project root
    project_root = Path(__file__).parent
    data_relative = Path(data_path).relative_to(project_root)
    models_relative = Path(models_path).relative_to(project_root)
    
    print(f"\nRelative to project root:")
    print(f"Data: {data_relative}")
    print(f"Models: {models_relative}")
    
    # Verify data directory structure
    data_dir = Path(data_path)
    if data_dir.exists():
        print(f"\nData directory contents:")
        for item in data_dir.iterdir():
            print(f"  {item.name}")
    else:
        print(f"\nData directory does not exist yet - will be created on first use")
    
    # Verify models directory structure
    models_dir = Path(models_path)
    if models_dir.exists():
        print(f"\nModels directory contents:")
        model_files = list(models_dir.glob("*.pkl"))
        if model_files:
            for model_file in model_files:
                print(f"  {model_file.name}")
        else:
            print("  No model files found")
    else:
        print(f"\nModels directory does not exist yet - will be created on first use")
    
    print("\n=== Path Configuration Test Complete ===")
    
    # Verify all paths point to project root
    expected_data = project_root / "data"
    expected_models = project_root / "models"
    
    success = True
    
    if Path(data_path) != expected_data:
        print(f"❌ ERROR: Data path mismatch!")
        print(f"   Expected: {expected_data}")
        print(f"   Got: {data_path}")
        success = False
    else:
        print(f"✅ Data path correctly configured")
    
    if Path(models_path) != expected_models:
        print(f"❌ ERROR: Models path mismatch!")
        print(f"   Expected: {expected_models}")
        print(f"   Got: {models_path}")
        success = False
    else:
        print(f"✅ Models path correctly configured")
    
    if success:
        print(f"\n🎉 ALL PATHS CORRECTLY CONFIGURED! 🎉")
        print(f"All data will be stored in: {project_root}/data/")
        print(f"All models will be stored in: {project_root}/models/")
    else:
        print(f"\n❌ PATH CONFIGURATION ERRORS FOUND")
    
    return success


def test_import_paths():
    """Test that all modules can import the config correctly"""
    print("\n=== Testing Module Imports ===")
    
    try:
        from data_acquisition import download_physionet_data, load_and_explore_data # type: ignore
        print("✅ data_acquisition imports working")
    except Exception as e:
        print(f"❌ data_acquisition import error: {e}")
    
    try:
        from pipeline.bci_pipeline import BCIPipeline # type: ignore
        print("✅ bci_pipeline imports working")
    except Exception as e:
        print(f"❌ bci_pipeline import error: {e}")
    
    try:
        from pipeline.improved_bci import ImprovedBCIPipeline # type: ignore
        print("✅ improved_bci imports working")
    except Exception as e:
        print(f"❌ improved_bci import error: {e}")
    
    try:
        from pipeline.streaming_bci import StreamingBCI # type: ignore
        print("✅ streaming_bci imports working")
    except Exception as e:
        print(f"❌ streaming_bci import error: {e}")
    
    print("✅ All module imports successful")


if __name__ == "__main__":
    success = test_data_paths()
    test_import_paths()
    
    if success:
        print(f"\n{'='*60}")
        print("✅ DATA PATH CONFIGURATION VERIFIED")
        print("All EEG data will be consistently stored in:")
        print(f"   {Path(__file__).parent}/data/")
        print("All trained models will be consistently stored in:")
        print(f"   {Path(__file__).parent}/models/")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print("❌ DATA PATH CONFIGURATION NEEDS FIXING")
        print(f"{'='*60}")
