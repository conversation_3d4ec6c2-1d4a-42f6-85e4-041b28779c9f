#!/usr/bin/env python3
"""
Total Perspective Vortex - Comprehensive BCI Visualization Demo
Demonstrates all visualization capabilities of the brain-computer interface system
"""

import sys
import argparse
import numpy as np # type: ignore
from pathlib import Path
import matplotlib.pyplot as plt # type: ignore

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from visualization.eeg_visualizer import EEGVisualizer, save_figure # type: ignore
from data_acquisition import load_and_explore_data # type: ignore
from preprocessing.eeg_preprocessing import EEGPreprocessor # type: ignore
from dimensionality.csp import CSP # type: ignore
from pipeline.improved_bci import ImprovedBCIPipeline # type: ignore
from config import get_data_path # type: ignore


def demo_raw_data_visualization(subject_id=1, run=4):
    """Demonstrate raw EEG data visualization"""
    print(f"\n{'='*60}")
    print("1. RAW EEG DATA VISUALIZATION")
    print(f"{'='*60}")
    
    # Initialize visualizer
    viz = EEGVisualizer()
    
    # Load raw data
    print(f"Loading Subject {subject_id}, Run {run}...")
    raw = load_and_explore_data(subject_id, run, get_data_path())
    
    # Plot raw EEG signals
    print("📊 Plotting raw EEG signals...")
    fig1 = viz.plot_raw_eeg(raw, duration=10, start=60, 
                           title=f"Raw EEG - Subject {subject_id}, Run {run}")
    save_figure(fig1, f"01_raw_eeg_s{subject_id}_r{run}.png")
    
    # Plot power spectrum
    print("📊 Plotting power spectral density...")
    fig2 = viz.plot_power_spectrum(raw, 
                                  title=f"Power Spectrum - Subject {subject_id}, Run {run}")
    save_figure(fig2, f"02_power_spectrum_s{subject_id}_r{run}.png")
    
    return raw


def demo_preprocessing_visualization(raw, subject_id=1, run=4):
    """Demonstrate preprocessing visualization"""
    print(f"\n{'='*60}")
    print("2. PREPROCESSING VISUALIZATION")
    print(f"{'='*60}")
    
    # Initialize visualizer and preprocessor
    viz = EEGVisualizer()
    preprocessor = EEGPreprocessor()
    
    # Apply filtering
    print("🔧 Applying preprocessing filters...")
    raw_filtered = preprocessor.filter_raw(raw)
    
    # Compare raw vs filtered
    print("📊 Plotting preprocessing comparison...")
    fig3 = viz.plot_preprocessing_comparison(raw, raw_filtered, channel='C3..')
    save_figure(fig3, f"03_preprocessing_comparison_s{subject_id}_r{run}.png")
    
    return raw_filtered


def demo_epochs_visualization(raw_filtered, subject_id=1, run=4):
    """Demonstrate epochs visualization"""
    print(f"\n{'='*60}")
    print("3. EPOCHS VISUALIZATION")
    print(f"{'='*60}")
    
    # Initialize visualizer and preprocessor
    viz = EEGVisualizer()
    preprocessor = EEGPreprocessor()
    
    # Get events and create epochs
    print("🔧 Creating epochs from filtered data...")
    import mne # type: ignore
    events, event_id = mne.events_from_annotations(raw_filtered, verbose=False)
    epochs = preprocessor.create_epochs(raw_filtered, events, event_id)
    
    if len(epochs) == 0:
        print("⚠️  No epochs found for this run")
        return None
    
    print(f"Created {len(epochs)} epochs")
    
    # Plot epochs overview
    print("📊 Plotting epochs overview...")
    fig4 = viz.plot_epochs_overview(epochs, 
                                   title=f"Epochs Overview - Subject {subject_id}, Run {run}")
    save_figure(fig4, f"04_epochs_overview_s{subject_id}_r{run}.png")
    
    return epochs


def demo_csp_visualization(epochs, subject_id=1, run=4):
    """Demonstrate CSP visualization"""
    print(f"\n{'='*60}")
    print("4. CSP SPATIAL PATTERNS VISUALIZATION")
    print(f"{'='*60}")
    
    if epochs is None:
        print("⚠️  No epochs available for CSP analysis")
        return None, None
    
    # Initialize visualizer
    viz = EEGVisualizer()
    
    # Get data and labels
    data = epochs.get_data()
    labels = epochs.events[:, -1]
    
    # Filter for binary classification
    mask = (labels == 2) | (labels == 3)
    if not np.any(mask):
        print("⚠️  No motor imagery events found")
        return None, None
    
    X_binary = data[mask]
    y_binary = labels[mask]
    y_binary = (y_binary == 3).astype(int)
    
    if len(np.unique(y_binary)) < 2:
        print("⚠️  Need at least 2 classes for CSP")
        return None, None
    
    print(f"Training CSP on {len(X_binary)} epochs with {len(np.unique(y_binary))} classes")
    
    # Train CSP
    csp = CSP(n_components=6, log=True)
    X_csp = csp.fit_transform(X_binary, y_binary)
    
    # Plot CSP patterns
    print("📊 Plotting CSP spatial patterns...")
    fig5 = viz.plot_csp_patterns(csp, epochs.info, 
                                title=f"CSP Patterns - Subject {subject_id}, Run {run}")
    save_figure(fig5, f"05_csp_patterns_s{subject_id}_r{run}.png")
    
    return csp, X_csp, y_binary


def demo_feature_space_visualization(X_csp, y_binary, subject_id=1, run=4):
    """Demonstrate feature space visualization"""
    print(f"\n{'='*60}")
    print("5. FEATURE SPACE VISUALIZATION")
    print(f"{'='*60}")
    
    if X_csp is None or y_binary is None:
        print("⚠️  No CSP features available")
        return
    
    # Initialize visualizer
    viz = EEGVisualizer()
    
    # Plot feature space
    print("📊 Plotting feature space...")
    fig6 = viz.plot_feature_space(X_csp, y_binary,
                                 title=f"CSP Feature Space - Subject {subject_id}, Run {run}")
    save_figure(fig6, f"06_feature_space_s{subject_id}_r{run}.png")


def demo_classification_visualization(subject_id=1, run=4):
    """Demonstrate classification results visualization"""
    print(f"\n{'='*60}")
    print("6. CLASSIFICATION RESULTS VISUALIZATION")
    print(f"{'='*60}")
    
    # Initialize visualizer and BCI pipeline
    viz = EEGVisualizer()
    
    try:
        # Train BCI pipeline
        print("🤖 Training BCI pipeline...")
        bci = ImprovedBCIPipeline(
            n_csp_components=4,
            use_spectral_features=False,  # Use CSP
            l_freq=8.0,
            h_freq=30.0
        )
        
        # Train on motor imagery runs
        train_runs = [4, 6, 8] if run not in [4, 6, 8] else [10, 12, 14]
        results = bci.train_and_evaluate(
            subject_ids=[subject_id],
            train_runs=train_runs,
            cv_folds=5
        )
        
        print(f"Training accuracy: {results['mean_cv_accuracy']:.3f}")
        
        # Make predictions on test run
        test_runs = [run]
        y_pred, y_proba, y_true = bci.predict([subject_id], test_runs)
        
        if len(y_pred) > 0:
            print(f"Test accuracy: {np.mean(y_pred == y_true):.3f}")
            
            # Plot classification results
            print("📊 Plotting classification results...")
            fig7 = viz.plot_classification_results(y_true, y_pred, y_proba,
                                                  title=f"Classification Results - Subject {subject_id}")
            save_figure(fig7, f"07_classification_results_s{subject_id}_r{run}.png")
        else:
            print("⚠️  No predictions available")
            
    except Exception as e:
        print(f"❌ Error in classification demo: {e}")


def run_complete_visualization_demo(subject_id=1, run=4, save_plots=True):
    """Run complete visualization demonstration"""
    print(f"🧠 TOTAL PERSPECTIVE VORTEX - VISUALIZATION DEMO")
    print(f"Subject: {subject_id}, Run: {run}")
    print(f"Save plots: {save_plots}")
    
    try:
        # 1. Raw data visualization
        raw = demo_raw_data_visualization(subject_id, run)
        
        # 2. Preprocessing visualization
        raw_filtered = demo_preprocessing_visualization(raw, subject_id, run)
        
        # 3. Epochs visualization
        epochs = demo_epochs_visualization(raw_filtered, subject_id, run)
        
        # 4. CSP visualization
        csp, X_csp, y_binary = demo_csp_visualization(epochs, subject_id, run)
        
        # 5. Feature space visualization
        demo_feature_space_visualization(X_csp, y_binary, subject_id, run)
        
        # 6. Classification visualization
        demo_classification_visualization(subject_id, run)
        
        print(f"\n{'='*60}")
        print("✅ VISUALIZATION DEMO COMPLETE!")
        print(f"{'='*60}")
        
        if save_plots:
            print(f"📁 All visualizations saved to: ./visualizations/")
            print(f"🖼️  Generated plots:")
            viz_dir = Path("visualizations")
            if viz_dir.exists():
                for plot_file in sorted(viz_dir.glob("*.png")):
                    print(f"   - {plot_file.name}")
        
        # Show plots if not saving
        if not save_plots:
            plt.show()
            
    except Exception as e:
        print(f"❌ Error in visualization demo: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description="Total Perspective Vortex BCI Visualization Demo",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python visualize_bci.py                    # Demo with default subject 1, run 4
  python visualize_bci.py --subject 2 --run 6  # Demo with subject 2, run 6
  python visualize_bci.py --no-save          # Show plots instead of saving
        """
    )
    
    parser.add_argument("--subject", type=int, default=1,
                       help="Subject ID (default: 1)")
    parser.add_argument("--run", type=int, default=4,
                       help="Run number (default: 4)")
    parser.add_argument("--no-save", action="store_true",
                       help="Show plots instead of saving them")
    
    args = parser.parse_args()
    
    # Run visualization demo
    run_complete_visualization_demo(
        subject_id=args.subject,
        run=args.run,
        save_plots=not args.no_save
    )


if __name__ == "__main__":
    main()
